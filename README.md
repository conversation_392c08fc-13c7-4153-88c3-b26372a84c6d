# 充电APP自动化测试框架

基于pytest + requests + allure的充电APP后端接口自动化测试框架。

## 项目结构

```
Testing_Europ/
├── config/                 # 配置文件
│   └── config.yaml        # 测试配置
├── tests/                 # 测试用例
│   ├── api/              # API接口测试
│   ├── business/         # 业务逻辑测试
│   ├── data/             # 测试数据
│   └── utils/            # 测试工具
├── utils/                # 工具类
│   ├── http_client.py    # HTTP客户端
│   ├── logger.py         # 日志工具
│   └── response_validator.py # 响应验证
├── logs/                 # 日志文件
├── reports/              # 测试报告
├── .github/workflows/    # CI/CD配置
├── conftest.py          # pytest配置
├── pytest.ini           # pytest配置文件
├── requirements.txt     # 依赖包
├── run_tests.py        # 测试执行脚本
└── README.md           # 项目说明
```

## 功能特性

### 🚀 核心功能
- **多层次测试**: API接口测试 + 业务逻辑测试
- **完整的测试报告**: HTML报告 + Allure报告
- **灵活的配置管理**: 支持多环境配置
- **强大的日志系统**: 分级日志记录
- **数据驱动测试**: 测试数据工厂模式

### 🛠️ 技术栈
- **pytest**: 测试框架
- **requests**: HTTP请求库
- **allure**: 测试报告
- **pydantic**: 数据验证
- **loguru**: 日志管理
- **faker**: 测试数据生成

### 📊 测试覆盖
- ✅ 接口功能测试
- ✅ 参数验证测试
- ✅ 错误处理测试
- ✅ 业务逻辑测试
- ✅ 边界值测试
- ✅ 并发测试
- ✅ 响应时间测试

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd Testing_Europ

# 安装依赖
pip install -r requirements.txt

# 安装Allure (macOS)
brew install allure

# 安装Allure (Windows)
# 下载并安装: https://github.com/allure-framework/allure2/releases
```

### 2. 配置设置

编辑 `config/config.yaml` 文件，配置测试环境：

```yaml
environments:
  test:
    base_url: "https://your-test-api.com"
    timeout: 30
    
auth:
  app_token: "your-app-token"
  platform: "android"
  version: "1.0.0"
```

### 3. 运行测试

```bash
# 使用测试脚本（推荐）
python run_tests.py --help

# 运行冒烟测试
python run_tests.py --smoke --env test

# 运行所有测试
python run_tests.py --all --env test

# 生成报告
python run_tests.py --report

# 查看Allure报告
python run_tests.py --serve
```

### 4. 直接使用pytest

```bash
# 运行冒烟测试
pytest -m smoke -v

# 运行API测试
pytest -m api -v

# 运行业务逻辑测试
pytest -m business -v

# 运行checkUpdate相关测试
pytest -m checkupdate -v

# 并行执行
pytest -n auto -v

# 生成Allure报告
pytest --alluredir=reports/allure-results
allure serve reports/allure-results
```

## 测试用例说明

### checkUpdate接口测试

#### API测试 (`tests/api/test_check_update_api.py`)
- ✅ 正常请求测试
- ✅ 无效请求头测试
- ✅ 无效请求参数测试
- ✅ 边界值测试
- ✅ 响应时间测试
- ✅ 并发请求测试
- ✅ 错误HTTP方法测试

#### 业务逻辑测试 (`tests/business/test_check_update_business.py`)
- ✅ 版本更新场景测试
- ✅ 强制更新逻辑测试
- ✅ 新版本提示逻辑测试
- ✅ 不同平台一致性测试
- ✅ 版本回退场景测试
- ✅ 业务规则边界测试

## 测试数据管理

### 数据工厂模式
使用 `tests/data/test_data_factory.py` 管理测试数据：

```python
# 获取有效请求数据
test_data = test_data_manager.get_test_data("valid_request")

# 获取无效参数数据
invalid_data = test_data_manager.get_test_data("invalid_params")

# 获取业务场景数据
scenarios = test_data_manager.get_test_data("business_scenario")
```

### 配置化数据
在 `config/config.yaml` 中配置业务规则：

```yaml
business_rules:
  version_update:
    force_update_threshold: 5  # 强制更新阈值
    new_update_threshold: 1    # 新版本提示阈值
```

## 报告查看

### HTML报告
- 位置: `reports/report.html`
- 包含: 测试结果、执行时间、错误信息

### Allure报告
- 生成: `allure generate reports/allure-results -o reports/allure-report`
- 查看: `allure serve reports/allure-results`
- 特性: 
  - 📊 测试趋势图
  - 🔍 详细步骤
  - 📎 请求响应附件
  - 🏷️ 测试分类

## CI/CD集成

### GitHub Actions
配置文件: `.github/workflows/api_tests.yml`

特性:
- 🔄 自动触发（Push/PR/定时）
- 🐍 多Python版本支持
- 📊 自动生成报告
- 📤 报告上传

### 本地集成
```bash
# 集成到现有CI系统
python run_tests.py --all --env test --report
```

## 扩展指南

### 添加新接口测试

1. **创建测试数据**
```python
# tests/data/test_data_factory.py
class NewApiDataFactory:
    @staticmethod
    def valid_request_data():
        return {...}
```

2. **创建API测试**
```python
# tests/api/test_new_api.py
class TestNewAPI:
    def test_valid_request(self):
        # 测试逻辑
        pass
```

3. **创建业务测试**
```python
# tests/business/test_new_api_business.py
class TestNewAPIBusiness:
    def test_business_logic(self):
        # 业务逻辑测试
        pass
```

### 添加新的验证器
```python
# utils/response_validator.py
class NewApiValidator:
    @staticmethod
    def validate_new_api_response(response):
        # 验证逻辑
        pass
```

## 最佳实践

### 1. 测试设计原则
- **单一职责**: 每个测试用例只验证一个功能点
- **独立性**: 测试用例之间不相互依赖
- **可重复**: 测试结果稳定可重复
- **清晰命名**: 测试名称清楚表达测试意图

### 2. 数据管理
- **数据隔离**: 测试数据与生产数据隔离
- **数据清理**: 测试后清理产生的数据
- **参数化**: 使用参数化测试覆盖多种场景

### 3. 错误处理
- **异常捕获**: 合理处理和记录异常
- **失败重试**: 对网络等不稳定因素进行重试
- **详细日志**: 记录足够的调试信息

### 4. 性能考虑
- **并行执行**: 使用pytest-xdist并行执行
- **合理超时**: 设置合适的请求超时时间
- **资源清理**: 及时关闭连接和清理资源

## 常见问题

### Q: 如何修改测试环境？
A: 修改 `config/config.yaml` 中的环境配置，或使用 `TEST_ENV` 环境变量。

### Q: 如何添加新的测试标记？
A: 在 `pytest.ini` 中添加新的 markers，然后在测试用例中使用 `@pytest.mark.your_marker`。

### Q: 如何查看详细的请求响应？
A: 查看 `logs/test_detail.log` 文件或Allure报告中的附件。

### Q: 如何在CI中运行测试？
A: 参考 `.github/workflows/api_tests.yml` 配置，或使用 `run_tests.py` 脚本。

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题，请联系测试团队或提交Issue。
