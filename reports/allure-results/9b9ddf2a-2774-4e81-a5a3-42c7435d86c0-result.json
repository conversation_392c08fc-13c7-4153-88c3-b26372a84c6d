{"name": "业务规则边界测试", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404", "trace": "self = <tests.business.test_check_update_business.TestCheckUpdateBusiness object at 0x10525b340>\n\n    @allure.title(\"业务规则边界测试\")\n    @allure.description(\"测试业务规则的边界条件\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.business\n    @pytest.mark.checkupdate\n    def test_business_rule_boundaries(self):\n        \"\"\"测试业务规则边界\"\"\"\n        base_headers = {\n            \"app-token\": \"valid-token-12345\",\n            \"platform\": \"android\",\n            \"version\": \"1.0.0\"\n        }\n    \n        # 测试各种边界条件\n        test_cases = [\n            {\n                \"version\": self.latest_version - self.force_threshold + 1,\n                \"expected_force\": False,\n                \"expected_new\": True,\n                \"description\": \"刚好不触发强制更新\"\n            },\n            {\n                \"version\": self.latest_version - self.force_threshold,\n                \"expected_force\": True,\n                \"expected_new\": True,\n                \"description\": \"刚好触发强制更新\"\n            },\n            {\n                \"version\": self.latest_version - self.new_threshold + 1,\n                \"expected_force\": False,\n                \"expected_new\": False,\n                \"description\": \"刚好不提示新版本\"\n            },\n            {\n                \"version\": self.latest_version - self.new_threshold,\n                \"expected_force\": False,\n                \"expected_new\": True,\n                \"description\": \"刚好提示新版本\"\n            }\n        ]\n    \n        for case in test_cases:\n            with allure.step(f\"测试边界条件: {case['description']}\"):\n                response = self.client.get(\n                    self.api_path,\n                    headers=base_headers,\n                    params={\"current_version\": case[\"version\"], \"os\": \"android\"}\n                )\n    \n>               json_data = ResponseValidator.validate_check_update_response(response)\n\ntests/business/test_check_update_business.py:281: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \nutils/response_validator.py:152: in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nresponse = <Response [404]>, expected_status = 200\n\n    @staticmethod\n    @allure.step(\"验证响应状态码\")\n    def validate_status_code(response, expected_status: int = 200):\n        \"\"\"验证响应状态码\"\"\"\n        actual_status = response.status_code\n        logger.info(f\"验证状态码: 期望={expected_status}, 实际={actual_status}\")\n    \n>       assert actual_status == expected_status, \\\n            f\"状态码不匹配: 期望={expected_status}, 实际={actual_status}\"\nE       AssertionError: 状态码不匹配: 期望=200, 实际=404\n\nutils/response_validator.py:50: AssertionError"}, "description": "测试业务规则的边界条件", "steps": [{"name": "测试边界条件: 刚好不触发强制更新", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/tests/business/test_check_update_business.py\", line 281, in test_business_rule_boundaries\n    json_data = ResponseValidator.validate_check_update_response(response)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/api/v1/app/version/checkUpdate", "status": "passed", "attachments": [{"name": "请求URL", "source": "1ec95c81-5682-4d41-b178-0b8705359c38-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "acf88316-22e8-4786-a647-68b17904548e-attachment.json", "type": "application/json"}], "start": 1750918185400, "stop": 1750918185401}], "attachments": [{"name": "响应信息", "source": "ad99a1dd-1494-47f0-b12c-1d67d531e56e-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/api/v1/app/version/checkUpdate'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 96, 'os': 'android'}"}], "start": 1750918185400, "stop": 1750918185673}, {"name": "完整验证checkUpdate响应", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "验证响应状态码", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918185674, "stop": 1750918185674}], "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "None"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918185674, "stop": 1750918185674}], "start": 1750918185400, "stop": 1750918185674}], "attachments": [{"name": "stdout", "source": "ab2f676b-a11c-4a73-9052-73081697cf31-attachment.txt", "type": "text/plain"}], "start": 1750918185400, "stop": 1750918185675, "uuid": "f995d2ef-bda2-4480-9285-ff5f225361ff", "historyId": "4207117db9283c00faf47eabf0404e3e", "testCaseId": "4207117db9283c00faf47eabf0404e3e", "fullName": "tests.business.test_check_update_business.TestCheckUpdateBusiness#test_business_rule_boundaries", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "checkUpdate业务逻辑测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "business"}, {"name": "parentSuite", "value": "tests.business"}, {"name": "suite", "value": "test_check_update_business"}, {"name": "subSuite", "value": "TestCheckUpdateBusiness"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.business.test_check_update_business"}]}