{"name": "边界值测试", "status": "passed", "description": "测试版本号边界值情况", "steps": [{"name": "测试场景: 最小版本号", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "f84df250-853c-46f6-9c30-ef8d41333266-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "f0eeb3ab-643c-4941-974e-8809b9dbe5e5-attachment.json", "type": "application/json"}], "start": 1750918174625, "stop": 1750918174625}], "attachments": [{"name": "响应信息", "source": "692381e9-66d3-4aa3-846b-7a819d514ba7-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "fda8ac8c-ee91-4553-953d-7d324b9e17c9-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 1, 'os': 'android'}"}], "start": 1750918174625, "stop": 1750918174891}], "start": 1750918174625, "stop": 1750918174891}, {"name": "验证响应", "status": "passed", "steps": [{"name": "完整验证checkUpdate响应", "status": "passed", "steps": [{"name": "验证响应状态码", "status": "passed", "attachments": [{"name": "状态码验证", "source": "48c6acc6-8972-4951-919c-75e0b6370ff9-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918174891, "stop": 1750918174892}, {"name": "验证响应JSON格式", "status": "passed", "attachments": [{"name": "响应JSON", "source": "1884d0c7-e466-4981-babc-cb7268c56f47-attachment.json", "type": "application/json"}], "parameters": [{"name": "response", "value": "<Response [200]>"}], "start": 1750918174892, "stop": 1750918174892}, {"name": "验证响应时间", "status": "passed", "attachments": [{"name": "响应时间验证", "source": "1fbb6844-2949-4977-b6e7-32d94ad00e0f-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "max_time", "value": "5.0"}], "start": 1750918174893, "stop": 1750918174893}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "None"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918174891, "stop": 1750918174893}], "start": 1750918174891, "stop": 1750918174893}], "attachments": [{"name": "stdout", "source": "67232b35-5131-4db2-8669-aa78b1b7506f-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_case", "value": "{'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, 'params': {'current_version': 1, 'os': 'android'}, 'case_name': '最小版本号'}"}], "start": 1750918174625, "stop": 1750918174894, "uuid": "c427d55a-0784-4d95-8fca-4eee96657c41", "historyId": "d44f0c72d00548dd9aeaafad8eea0c0a", "testCaseId": "fd0e01b50a673a6d9d3514f050ffc2de", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_boundary_values", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}