{"name": "不同HTTP方法测试", "status": "passed", "description": "测试使用错误HTTP方法访问接口", "steps": [{"name": "使用PATCH方法请求", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "PATCH https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "6c77dbae-e4ba-410b-bba3-e37a29e2ec66-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "34bea628-d0e6-4921-933c-d6aca82ce21f-attachment.json", "type": "application/json"}], "start": 1750918179729, "stop": 1750918179729}], "attachments": [{"name": "响应信息", "source": "e3f18c6a-1630-49d2-8558-cc06a6534257-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'PATCH'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 61, 'os': 'ios'}"}], "start": 1750918179729, "stop": 1750918179994}], "start": 1750918179729, "stop": 1750918179994}, {"name": "验证方法不允许错误", "status": "passed", "start": 1750918179994, "stop": 1750918179994}], "attachments": [{"name": "stdout", "source": "40b6d8ca-1869-436d-8e9a-a1df2a5837cf-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'PATCH'"}], "start": 1750918179729, "stop": 1750918179995, "uuid": "ac0ce2a0-4e48-4a4d-876d-ad3304541051", "historyId": "62c4bf65fd9a9ef1bc0938662e0f0a8a", "testCaseId": "c3832e1226d756e1e251bfcf7c9c5aa5", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_wrong_http_methods", "labels": [{"name": "severity", "value": "minor"}, {"name": "epic", "value": "充电APP后端接口测试"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}