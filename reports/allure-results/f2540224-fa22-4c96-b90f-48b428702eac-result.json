{"name": "不同HTTP方法测试", "status": "passed", "description": "测试使用错误HTTP方法访问接口", "steps": [{"name": "使用DELETE方法请求", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "DELETE https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "8c3e2ce4-08af-4ca4-b833-9ecb09dc3050-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "4af94abf-715f-48a7-8742-0c9228bd44f1-attachment.json", "type": "application/json"}], "start": 1750918179450, "stop": 1750918179450}], "attachments": [{"name": "响应信息", "source": "21666ff7-dcc0-407b-a671-1b91b5987a1f-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'DELETE'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 12, 'os': 'ios'}"}], "start": 1750918179450, "stop": 1750918179725}], "start": 1750918179450, "stop": 1750918179725}, {"name": "验证方法不允许错误", "status": "passed", "start": 1750918179725, "stop": 1750918179725}], "attachments": [{"name": "stdout", "source": "7ab6aeea-629f-478f-bbe4-6234e5af8375-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'DELETE'"}], "start": 1750918179450, "stop": 1750918179725, "uuid": "2df15066-9fbc-4ebc-a18b-ff71470abbba", "historyId": "81d3f25873419b2052d1f05ebb6f07ac", "testCaseId": "c3832e1226d756e1e251bfcf7c9c5aa5", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_wrong_http_methods", "labels": [{"name": "severity", "value": "minor"}, {"name": "epic", "value": "充电APP后端接口测试"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}