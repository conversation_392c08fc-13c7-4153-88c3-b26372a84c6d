{"name": "强制更新逻辑测试", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404", "trace": "self = <tests.business.test_check_update_business.TestCheckUpdateBusiness object at 0x10526b4f0>\n\n    @allure.title(\"强制更新逻辑测试\")\n    @allure.description(\"测试强制更新的触发条件\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.business\n    @pytest.mark.checkupdate\n    def test_force_update_logic(self):\n        \"\"\"测试强制更新逻辑\"\"\"\n        base_headers = {\n            \"app-token\": \"valid-token-12345\",\n            \"platform\": \"android\",\n            \"version\": \"1.0.0\"\n        }\n    \n        # 测试刚好达到强制更新阈值\n        threshold_version = self.latest_version - self.force_threshold\n    \n        with allure.step(f\"测试版本{threshold_version}（刚好达到强制更新阈值）\"):\n            response = self.client.get(\n                self.api_path,\n                headers=base_headers,\n                params={\"current_version\": threshold_version, \"os\": \"android\"}\n            )\n    \n>           json_data = ResponseValidator.validate_check_update_response(response)\n\ntests/business/test_check_update_business.py:90: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \nutils/response_validator.py:152: in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nresponse = <Response [404]>, expected_status = 200\n\n    @staticmethod\n    @allure.step(\"验证响应状态码\")\n    def validate_status_code(response, expected_status: int = 200):\n        \"\"\"验证响应状态码\"\"\"\n        actual_status = response.status_code\n        logger.info(f\"验证状态码: 期望={expected_status}, 实际={actual_status}\")\n    \n>       assert actual_status == expected_status, \\\n            f\"状态码不匹配: 期望={expected_status}, 实际={actual_status}\"\nE       AssertionError: 状态码不匹配: 期望=200, 实际=404\n\nutils/response_validator.py:50: AssertionError"}, "description": "测试强制更新的触发条件", "steps": [{"name": "测试版本95（刚好达到强制更新阈值）", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/tests/business/test_check_update_business.py\", line 90, in test_force_update_logic\n    json_data = ResponseValidator.validate_check_update_response(response)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/api/v1/app/version/checkUpdate", "status": "passed", "attachments": [{"name": "请求URL", "source": "450b228d-2bf4-42bf-a151-684606b4f9c3-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "5b58655b-4b75-4bca-8b4e-d959338626d6-attachment.json", "type": "application/json"}], "start": 1750918182397, "stop": 1750918182397}], "attachments": [{"name": "响应信息", "source": "b336a347-2673-4380-996a-1d2e176e51d7-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/api/v1/app/version/checkUpdate'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 95, 'os': 'android'}"}], "start": 1750918182397, "stop": 1750918182832}, {"name": "完整验证checkUpdate响应", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "验证响应状态码", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918182832, "stop": 1750918182832}], "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "None"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918182832, "stop": 1750918182832}], "start": 1750918182397, "stop": 1750918182833}], "attachments": [{"name": "stdout", "source": "d77f0b5e-da76-43bb-97ff-7a4cadb5944a-attachment.txt", "type": "text/plain"}], "start": 1750918182397, "stop": 1750918182833, "uuid": "5a205f7d-5f05-4f90-8f84-60b62a9c278b", "historyId": "d3fee69fb75521ab1a92afd48d12744b", "testCaseId": "d3fee69fb75521ab1a92afd48d12744b", "fullName": "tests.business.test_check_update_business.TestCheckUpdateBusiness#test_force_update_logic", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "checkUpdate业务逻辑测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "business"}, {"name": "parentSuite", "value": "tests.business"}, {"name": "suite", "value": "test_check_update_business"}, {"name": "subSuite", "value": "TestCheckUpdateBusiness"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.business.test_check_update_business"}]}