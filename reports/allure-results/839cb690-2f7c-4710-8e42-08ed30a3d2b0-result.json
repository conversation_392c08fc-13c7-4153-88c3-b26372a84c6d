{"name": "正常请求测试", "status": "passed", "description": "测试使用有效参数请求checkUpdate接口", "steps": [{"name": "发送checkUpdate请求", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "dc42477f-b0d1-4b0d-a4f0-1c0657963779-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "89188457-0d47-4b1b-8629-556203a31f61-attachment.json", "type": "application/json"}], "start": 1750918169151, "stop": 1750918169151}], "attachments": [{"name": "响应信息", "source": "b24571e1-8665-4fe8-bf53-a6df25c35d77-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "aa6c1667-9206-4fab-ba5d-3c14f4475595-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 23, 'os': 'ios'}"}], "start": 1750918169151, "stop": 1750918170310}], "start": 1750918169150, "stop": 1750918170310}, {"name": "验证响应", "status": "passed", "steps": [{"name": "完整验证checkUpdate响应", "status": "passed", "steps": [{"name": "验证响应状态码", "status": "passed", "attachments": [{"name": "状态码验证", "source": "b34cccfd-731b-474a-b2cf-3071ef7ef3eb-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918170310, "stop": 1750918170311}, {"name": "验证响应JSON格式", "status": "passed", "attachments": [{"name": "响应JSON", "source": "712fd04e-63c3-4b4d-9d71-5cae8f2bdbfc-attachment.json", "type": "application/json"}], "parameters": [{"name": "response", "value": "<Response [200]>"}], "start": 1750918170311, "stop": 1750918170312}, {"name": "验证响应时间", "status": "passed", "attachments": [{"name": "响应时间验证", "source": "7c5f214e-f24c-4365-861a-4f1fb6fd9185-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "max_time", "value": "5.0"}], "start": 1750918170312, "stop": 1750918170313}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "None"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918170310, "stop": 1750918170313}], "start": 1750918170310, "stop": 1750918170313}], "attachments": [{"name": "stdout", "source": "a23d4f8f-3991-44cf-bd70-23968327035f-attachment.txt", "type": "text/plain"}], "start": 1750918169150, "stop": 1750918170313, "uuid": "abe56b0e-014b-411e-a258-ddc160ada9f4", "historyId": "902dcd5ace81e8cff1d816513f3722b8", "testCaseId": "902dcd5ace81e8cff1d816513f3722b8", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_valid_request", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "severity", "value": "blocker"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}