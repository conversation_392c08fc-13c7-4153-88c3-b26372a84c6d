2025-06-26 14:09:35 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_boundary_values[test_case2]
2025-06-26 14:09:35 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:35 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:35 | INFO     | utils.http_client:request:138 - 请求耗时: 0.270秒
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=200
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_json_format:64 - 响应JSON格式验证通过
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_check_update_response:164 - httpbin.org响应格式验证通过
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_response_time:134 - 响应时间验证: 0.269秒 (最大允许: 5.0秒)
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_check_update_response:169 - 演示版本响应验证通过
2025-06-26 14:09:35 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_boundary_values[test_case2]
