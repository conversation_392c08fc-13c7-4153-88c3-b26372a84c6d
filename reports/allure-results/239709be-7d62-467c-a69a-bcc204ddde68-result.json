{"name": "无效请求参数测试", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=200", "trace": "self = <tests.api.test_check_update_api.TestCheckUpdateAPI object at 0x105034280>\ntest_case = {'case_name': 'current_version为0', 'expected_status': 400, 'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, 'params': {'current_version': 0, 'os': 'android'}}\n\n    @allure.title(\"无效请求参数测试\")\n    @allure.description(\"测试使用无效请求参数的情况\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.api\n    @pytest.mark.checkupdate\n    @pytest.mark.parametrize(\"test_case\", test_data_manager.get_test_data(\"invalid_params\"))\n    def test_invalid_params(self, test_case):\n        \"\"\"测试无效请求参数\"\"\"\n        with allure.step(f\"测试场景: {test_case['case_name']}\"):\n            response = self.client.get(\n                self.api_path,\n                headers=test_case[\"headers\"],\n                params=test_case[\"params\"]\n            )\n    \n        with allure.step(\"验证错误响应\"):\n>           ResponseValidator.validate_status_code(\n                response,\n                test_case[\"expected_status\"]\n            )\n\ntests/api/test_check_update_api.py:84: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nresponse = <Response [200]>, expected_status = 400\n\n    @staticmethod\n    @allure.step(\"验证响应状态码\")\n    def validate_status_code(response, expected_status: int = 200):\n        \"\"\"验证响应状态码\"\"\"\n        actual_status = response.status_code\n        logger.info(f\"验证状态码: 期望={expected_status}, 实际={actual_status}\")\n    \n>       assert actual_status == expected_status, \\\n            f\"状态码不匹配: 期望={expected_status}, 实际={actual_status}\"\nE       AssertionError: 状态码不匹配: 期望=400, 实际=200\n\nutils/response_validator.py:50: AssertionError"}, "description": "测试使用无效请求参数的情况", "steps": [{"name": "测试场景: current_version为0", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "e483d41b-c8f6-4ab9-865d-7037284f2b5e-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "bf2ae02a-7cf2-48cd-b850-a56e1c5d86e0-attachment.json", "type": "application/json"}], "start": 1750918172225, "stop": 1750918172225}], "attachments": [{"name": "响应信息", "source": "42a3ce45-1068-453d-b7ac-8dc2395996a0-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "5dc0d151-4134-4ab1-ac8c-15796de0d311-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 0, 'os': 'android'}"}], "start": 1750918172225, "stop": 1750918172493}], "start": 1750918172225, "stop": 1750918172494}, {"name": "验证错误响应", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=200\n", "trace": "  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/tests/api/test_check_update_api.py\", line 84, in test_invalid_params\n    ResponseValidator.validate_status_code(\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "验证响应状态码", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=200\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "400"}], "start": 1750918172494, "stop": 1750918172494}], "start": 1750918172494, "stop": 1750918172494}], "attachments": [{"name": "stdout", "source": "a6afc038-b561-42ea-adfa-462a2482d27b-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_case", "value": "{'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, 'params': {'current_version': 0, 'os': 'android'}, 'expected_status': 400, 'case_name': 'current_version为0'}"}], "start": 1750918172225, "stop": 1750918172495, "uuid": "8f328092-666f-43f3-ab95-c0b65801605f", "historyId": "c3724882f481384b475fb77f2a68b251", "testCaseId": "2004d878129a87d8311d1f52b8025f55", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_invalid_params", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}