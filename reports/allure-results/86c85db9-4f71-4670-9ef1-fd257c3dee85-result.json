{"name": "边界值测试", "status": "passed", "description": "测试版本号边界值情况", "steps": [{"name": "测试场景: 最大版本号", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "f2f05c3d-859f-4727-af83-151a0b159673-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "be96b133-cafc-4463-871f-ab9203f7d992-attachment.json", "type": "application/json"}], "start": 1750918174897, "stop": 1750918174897}], "attachments": [{"name": "响应信息", "source": "165c0a66-ca2e-452e-afee-b3f3d5e3f3ff-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "d62ebf61-0af1-4cb5-bdfc-08a2c1ef8d58-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 999, 'os': 'android'}"}], "start": 1750918174897, "stop": 1750918175163}], "start": 1750918174897, "stop": 1750918175163}, {"name": "验证响应", "status": "passed", "steps": [{"name": "完整验证checkUpdate响应", "status": "passed", "steps": [{"name": "验证响应状态码", "status": "passed", "attachments": [{"name": "状态码验证", "source": "f5000371-4701-4a99-99f5-38c853b5c8ad-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918175164, "stop": 1750918175164}, {"name": "验证响应JSON格式", "status": "passed", "attachments": [{"name": "响应JSON", "source": "d1504892-38ab-48c3-95e9-58f75043954c-attachment.json", "type": "application/json"}], "parameters": [{"name": "response", "value": "<Response [200]>"}], "start": 1750918175164, "stop": 1750918175165}, {"name": "验证响应时间", "status": "passed", "attachments": [{"name": "响应时间验证", "source": "837b9bb8-bad5-41f0-8d0c-4596466b214b-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "max_time", "value": "5.0"}], "start": 1750918175166, "stop": 1750918175166}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "None"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918175163, "stop": 1750918175166}], "start": 1750918175163, "stop": 1750918175166}], "attachments": [{"name": "stdout", "source": "4b7ebf38-7433-401a-9c90-62a712e0a341-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_case", "value": "{'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, 'params': {'current_version': 999, 'os': 'android'}, 'case_name': '最大版本号'}"}], "start": 1750918174897, "stop": 1750918175167, "uuid": "7cc24492-7515-489b-a751-4aef1e6fd72b", "historyId": "ca18416fdef998983d98627aa29b36a8", "testCaseId": "fd0e01b50a673a6d9d3514f050ffc2de", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_boundary_values", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}