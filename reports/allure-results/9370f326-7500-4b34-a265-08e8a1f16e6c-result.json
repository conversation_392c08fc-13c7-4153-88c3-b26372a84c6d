{"name": "响应时间测试", "status": "passed", "description": "测试接口响应时间是否在合理范围内", "steps": [{"name": "发送请求并测量响应时间", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "28816b3d-eac6-42f0-8903-c3210097a436-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "59225898-3e52-4b2d-901c-35395cdaeb65-attachment.json", "type": "application/json"}], "start": 1750918175449, "stop": 1750918175449}], "attachments": [{"name": "响应信息", "source": "618f3cdb-8e40-4a1a-a1d8-5a131e0fc5ef-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "781f83f7-7a6d-450c-bd4d-3acc1a86a5c2-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 35, 'os': 'android'}"}], "start": 1750918175449, "stop": 1750918176143}], "start": 1750918175449, "stop": 1750918176143}, {"name": "验证响应时间", "status": "passed", "steps": [{"name": "验证响应时间", "status": "passed", "attachments": [{"name": "响应时间验证", "source": "f32328a1-4d17-492b-a1e4-1dc83f62988d-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "max_time", "value": "3.0"}], "start": 1750918176143, "stop": 1750918176144}], "start": 1750918176143, "stop": 1750918176145}], "attachments": [{"name": "stdout", "source": "db5016f4-9af3-48d0-9d05-e9bd28e07c4c-attachment.txt", "type": "text/plain"}], "start": 1750918175448, "stop": 1750918176145, "uuid": "2d285a34-83db-451e-ba8a-37157775d8d5", "historyId": "ad12de4243656b42934fb9e5ce3f7e9f", "testCaseId": "ad12de4243656b42934fb9e5ce3f7e9f", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_response_time", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}