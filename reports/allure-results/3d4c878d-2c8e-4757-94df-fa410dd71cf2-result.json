{"name": "边界值测试", "status": "passed", "description": "测试版本号边界值情况", "steps": [{"name": "测试场景: 超大版本号", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "805ad324-7326-4799-884a-827720f4b7d9-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "6c7699b9-b312-4480-b5ce-74fefd630de1-attachment.json", "type": "application/json"}], "start": 1750918175170, "stop": 1750918175170}], "attachments": [{"name": "响应信息", "source": "1ab7501c-989e-4ac0-8896-b0938d7b0a66-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "f644e3e0-ca42-4219-8e37-a613d7d34b3e-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 99999, 'os': 'android'}"}], "start": 1750918175170, "stop": 1750918175442}], "start": 1750918175170, "stop": 1750918175442}, {"name": "验证响应", "status": "passed", "steps": [{"name": "完整验证checkUpdate响应", "status": "passed", "steps": [{"name": "验证响应状态码", "status": "passed", "attachments": [{"name": "状态码验证", "source": "f4f8a3f9-95bd-4861-8644-bbdf8fba4eb7-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918175443, "stop": 1750918175443}, {"name": "验证响应JSON格式", "status": "passed", "attachments": [{"name": "响应JSON", "source": "9f39b29c-49af-492e-95c5-1767f3606d7c-attachment.json", "type": "application/json"}], "parameters": [{"name": "response", "value": "<Response [200]>"}], "start": 1750918175443, "stop": 1750918175444}, {"name": "验证响应时间", "status": "passed", "attachments": [{"name": "响应时间验证", "source": "0703d5ef-02fb-4b40-8999-d185d2ac5e30-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "max_time", "value": "5.0"}], "start": 1750918175444, "stop": 1750918175445}], "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "None"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918175443, "stop": 1750918175445}], "start": 1750918175443, "stop": 1750918175445}], "attachments": [{"name": "stdout", "source": "9ecc9238-96f1-409a-87b1-350ea1f0e6d9-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_case", "value": "{'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, 'params': {'current_version': 99999, 'os': 'android'}, 'case_name': '超大版本号'}"}], "start": 1750918175170, "stop": 1750918175446, "uuid": "6c5faed5-50ac-4683-9429-d1ce35eab091", "historyId": "0138936ada9f644fb34f83f2d663aa0b", "testCaseId": "fd0e01b50a673a6d9d3514f050ffc2de", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_boundary_values", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}