{"name": "版本更新业务场景测试", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404", "trace": "self = <tests.business.test_check_update_business.TestCheckUpdateBusiness object at 0x10526b3d0>\nscenario = {'case_name': '版本过旧需要强制更新', 'description': '版本差距过大，需要强制更新', 'expected_result': {'force_update': True, 'new_update': True}, 'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, ...}\n\n    @allure.title(\"版本更新业务场景测试\")\n    @allure.description(\"测试不同版本差距下的更新提示逻辑\")\n    @allure.severity(allure.severity_level.BLOCKER)\n    @pytest.mark.business\n    @pytest.mark.checkupdate\n    @pytest.mark.critical\n    @pytest.mark.parametrize(\"scenario\", test_data_manager.get_test_data(\"business_scenario\"))\n    def test_version_update_scenarios(self, scenario):\n        \"\"\"测试版本更新业务场景\"\"\"\n        with allure.step(f\"业务场景: {scenario['case_name']}\"):\n            allure.attach(scenario['description'], \"场景描述\", allure.attachment_type.TEXT)\n    \n            response = self.client.get(\n                self.api_path,\n                headers=scenario[\"headers\"],\n                params=scenario[\"params\"]\n            )\n    \n        with allure.step(\"验证响应格式\"):\n>           json_data = ResponseValidator.validate_check_update_response(\n                response,\n                expected_values=scenario.get(\"expected_result\")\n            )\n\ntests/business/test_check_update_business.py:50: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \nutils/response_validator.py:152: in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nresponse = <Response [404]>, expected_status = 200\n\n    @staticmethod\n    @allure.step(\"验证响应状态码\")\n    def validate_status_code(response, expected_status: int = 200):\n        \"\"\"验证响应状态码\"\"\"\n        actual_status = response.status_code\n        logger.info(f\"验证状态码: 期望={expected_status}, 实际={actual_status}\")\n    \n>       assert actual_status == expected_status, \\\n            f\"状态码不匹配: 期望={expected_status}, 实际={actual_status}\"\nE       AssertionError: 状态码不匹配: 期望=200, 实际=404\n\nutils/response_validator.py:50: AssertionError"}, "description": "测试不同版本差距下的更新提示逻辑", "steps": [{"name": "业务场景: 版本过旧需要强制更新", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/api/v1/app/version/checkUpdate", "status": "passed", "attachments": [{"name": "请求URL", "source": "9d1dc5c9-c708-46cc-bca7-3aebf94e02d0-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "5b002a73-ef4c-4a70-9e4b-53d194d9983c-attachment.json", "type": "application/json"}], "start": 1750918181024, "stop": 1750918181024}], "attachments": [{"name": "响应信息", "source": "6488a06c-edcf-40f7-af4c-47e25be91e45-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/api/v1/app/version/checkUpdate'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 90, 'os': 'android'}"}], "start": 1750918181024, "stop": 1750918182095}], "attachments": [{"name": "场景描述", "source": "10a67cdf-bec4-4ad2-aab1-47529d90003b-attachment.txt", "type": "text/plain"}], "start": 1750918181024, "stop": 1750918182095}, {"name": "验证响应格式", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/tests/business/test_check_update_business.py\", line 50, in test_version_update_scenarios\n    json_data = ResponseValidator.validate_check_update_response(\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "完整验证checkUpdate响应", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "验证响应状态码", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918182095, "stop": 1750918182096}], "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "{'force_update': True, 'new_update': True}"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918182095, "stop": 1750918182096}], "start": 1750918182095, "stop": 1750918182096}], "attachments": [{"name": "stdout", "source": "c8fb25ba-49e5-4678-a265-73f47ef44d3b-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "scenario", "value": "{'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, 'params': {'current_version': 90, 'os': 'android'}, 'expected_result': {'force_update': True, 'new_update': True}, 'case_name': '版本过旧需要强制更新', 'description': '版本差距过大，需要强制更新'}"}], "start": 1750918181024, "stop": 1750918182097, "uuid": "fa1f6322-96fb-4db6-87ea-c490b7532d1d", "historyId": "68d12a48845c66f38a3a714cb92c8f59", "testCaseId": "8fcb190d899c26562d9baf89497d133c", "fullName": "tests.business.test_check_update_business.TestCheckUpdateBusiness#test_version_update_scenarios", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "story", "value": "checkUpdate业务逻辑测试"}, {"name": "feature", "value": "版本检查"}, {"name": "severity", "value": "blocker"}, {"name": "tag", "value": "critical"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "business"}, {"name": "parentSuite", "value": "tests.business"}, {"name": "suite", "value": "test_check_update_business"}, {"name": "subSuite", "value": "TestCheckUpdateBusiness"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.business.test_check_update_business"}]}