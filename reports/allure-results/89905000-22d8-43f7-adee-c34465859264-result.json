{"name": "无效请求参数测试", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=200", "trace": "self = <tests.api.test_check_update_api.TestCheckUpdateAPI object at 0x1051d4fd0>\ntest_case = {'case_name': 'os为空字符串', 'expected_status': 400, 'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, 'params': {'current_version': 1, 'os': ''}}\n\n    @allure.title(\"无效请求参数测试\")\n    @allure.description(\"测试使用无效请求参数的情况\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.api\n    @pytest.mark.checkupdate\n    @pytest.mark.parametrize(\"test_case\", test_data_manager.get_test_data(\"invalid_params\"))\n    def test_invalid_params(self, test_case):\n        \"\"\"测试无效请求参数\"\"\"\n        with allure.step(f\"测试场景: {test_case['case_name']}\"):\n            response = self.client.get(\n                self.api_path,\n                headers=test_case[\"headers\"],\n                params=test_case[\"params\"]\n            )\n    \n        with allure.step(\"验证错误响应\"):\n>           ResponseValidator.validate_status_code(\n                response,\n                test_case[\"expected_status\"]\n            )\n\ntests/api/test_check_update_api.py:84: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nresponse = <Response [200]>, expected_status = 400\n\n    @staticmethod\n    @allure.step(\"验证响应状态码\")\n    def validate_status_code(response, expected_status: int = 200):\n        \"\"\"验证响应状态码\"\"\"\n        actual_status = response.status_code\n        logger.info(f\"验证状态码: 期望={expected_status}, 实际={actual_status}\")\n    \n>       assert actual_status == expected_status, \\\n            f\"状态码不匹配: 期望={expected_status}, 实际={actual_status}\"\nE       AssertionError: 状态码不匹配: 期望=400, 实际=200\n\nutils/response_validator.py:50: AssertionError"}, "description": "测试使用无效请求参数的情况", "steps": [{"name": "测试场景: os为空字符串", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "15a940dc-0fec-4f14-a983-5e6d43eb3a04-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "eaaba4db-6fa2-4641-8100-9f54e87935a0-attachment.json", "type": "application/json"}], "start": 1750918174351, "stop": 1750918174351}], "attachments": [{"name": "响应信息", "source": "8f233b52-2a3d-4152-8189-0fefe772ab59-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "5d303089-1f44-4faa-a18d-e8e7c18f7369-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 1, 'os': ''}"}], "start": 1750918174351, "stop": 1750918174617}], "start": 1750918174351, "stop": 1750918174617}, {"name": "验证错误响应", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=200\n", "trace": "  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/tests/api/test_check_update_api.py\", line 84, in test_invalid_params\n    ResponseValidator.validate_status_code(\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "验证响应状态码", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=200\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "parameters": [{"name": "response", "value": "<Response [200]>"}, {"name": "expected_status", "value": "400"}], "start": 1750918174617, "stop": 1750918174617}], "start": 1750918174617, "stop": 1750918174617}], "attachments": [{"name": "stdout", "source": "ed28b36b-4c4a-418c-9b83-7a869abd9fe5-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_case", "value": "{'headers': {'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}, 'params': {'current_version': 1, 'os': ''}, 'expected_status': 400, 'case_name': 'os为空字符串'}"}], "start": 1750918174351, "stop": 1750918174618, "uuid": "368578ea-3c2a-47ce-959d-04ae6a9b3476", "historyId": "79feeaf70391372f392b1798ba222f72", "testCaseId": "2004d878129a87d8311d1f52b8025f55", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_invalid_params", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}