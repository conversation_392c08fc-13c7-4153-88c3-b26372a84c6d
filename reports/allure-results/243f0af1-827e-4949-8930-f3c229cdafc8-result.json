{"name": "不同HTTP方法测试", "status": "passed", "description": "测试使用错误HTTP方法访问接口", "steps": [{"name": "使用POST方法请求", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "POST https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "0d2f8504-0577-4dad-b8f8-31f579209a3c-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "bfdb70ea-f577-4693-af8d-6f42336a524a-attachment.json", "type": "application/json"}], "start": 1750918178071, "stop": 1750918178071}], "attachments": [{"name": "响应信息", "source": "1b294257-63ad-475d-9566-161b15a127b0-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'POST'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 64, 'os': 'ios'}"}], "start": 1750918178071, "stop": 1750918178816}], "start": 1750918178071, "stop": 1750918178816}, {"name": "验证方法不允许错误", "status": "passed", "start": 1750918178816, "stop": 1750918178816}], "attachments": [{"name": "stdout", "source": "1b606b1e-ed80-46ab-a545-912a93679853-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'POST'"}], "start": 1750918178071, "stop": 1750918178816, "uuid": "9082d8a5-cefc-4482-9344-3da983cd6611", "historyId": "530b8a6878ecf09c331ea4bc316516a1", "testCaseId": "c3832e1226d756e1e251bfcf7c9c5aa5", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_wrong_http_methods", "labels": [{"name": "severity", "value": "minor"}, {"name": "epic", "value": "充电APP后端接口测试"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}