{"name": "不同平台业务逻辑测试", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404", "trace": "self = <tests.business.test_check_update_business.TestCheckUpdateBusiness object at 0x10526bcd0>\nplatform = 'ios', os = 'ios'\n\n    @allure.title(\"不同平台业务逻辑测试\")\n    @allure.description(\"测试Android和iOS平台的业务逻辑一致性\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.business\n    @pytest.mark.checkupdate\n    @pytest.mark.parametrize(\"platform,os\", [(\"android\", \"android\"), (\"ios\", \"ios\")])\n    def test_platform_consistency(self, platform, os):\n        \"\"\"测试不同平台的业务逻辑一致性\"\"\"\n        headers = {\n            \"app-token\": \"valid-token-12345\",\n            \"platform\": platform,\n            \"version\": \"1.0.0\"\n        }\n    \n        test_version = self.latest_version - 3  # 测试一个中等版本差距\n    \n        with allure.step(f\"测试{platform}平台版本{test_version}\"):\n            response = self.client.get(\n                self.api_path,\n                headers=headers,\n                params={\"current_version\": test_version, \"os\": os}\n            )\n    \n>           json_data = ResponseValidator.validate_check_update_response(response)\n\ntests/business/test_check_update_business.py:182: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \nutils/response_validator.py:152: in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nresponse = <Response [404]>, expected_status = 200\n\n    @staticmethod\n    @allure.step(\"验证响应状态码\")\n    def validate_status_code(response, expected_status: int = 200):\n        \"\"\"验证响应状态码\"\"\"\n        actual_status = response.status_code\n        logger.info(f\"验证状态码: 期望={expected_status}, 实际={actual_status}\")\n    \n>       assert actual_status == expected_status, \\\n            f\"状态码不匹配: 期望={expected_status}, 实际={actual_status}\"\nE       AssertionError: 状态码不匹配: 期望=200, 实际=404\n\nutils/response_validator.py:50: AssertionError"}, "description": "测试Android和iOS平台的业务逻辑一致性", "steps": [{"name": "测试ios平台版本97", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/tests/business/test_check_update_business.py\", line 182, in test_platform_consistency\n    json_data = ResponseValidator.validate_check_update_response(response)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/api/v1/app/version/checkUpdate", "status": "passed", "attachments": [{"name": "请求URL", "source": "76abd4db-186d-4643-9b7e-178bc35de77e-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "4a17ff0f-ef58-436f-bf5a-1e0d618eae40-attachment.json", "type": "application/json"}], "start": 1750918183458, "stop": 1750918183458}], "attachments": [{"name": "响应信息", "source": "387390ec-9509-46f0-9a46-d594ec6f82fc-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/api/v1/app/version/checkUpdate'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 97, 'os': 'ios'}"}], "start": 1750918183457, "stop": 1750918184340}, {"name": "完整验证checkUpdate响应", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "验证响应状态码", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918184340, "stop": 1750918184340}], "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "None"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918184340, "stop": 1750918184340}], "start": 1750918183457, "stop": 1750918184341}], "attachments": [{"name": "stdout", "source": "8527ba6f-4d83-4c42-bb6f-0b48971ec534-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "platform", "value": "'ios'"}, {"name": "os", "value": "'ios'"}], "start": 1750918183457, "stop": 1750918184341, "uuid": "72fc7bc6-9ff1-4f1b-bcdd-24df4144f9e3", "historyId": "15a4b6b166c1b520832bff0209b007e7", "testCaseId": "af85298158727cfb1a270ae7d8ccc667", "fullName": "tests.business.test_check_update_business.TestCheckUpdateBusiness#test_platform_consistency", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "checkUpdate业务逻辑测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "business"}, {"name": "parentSuite", "value": "tests.business"}, {"name": "suite", "value": "test_check_update_business"}, {"name": "subSuite", "value": "TestCheckUpdateBusiness"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.business.test_check_update_business"}]}