{"name": "版本回退场景测试", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=404", "trace": "self = <tests.business.test_check_update_business.TestCheckUpdateBusiness object at 0x1051d4460>\n\n    @allure.title(\"版本回退场景测试\")\n    @allure.description(\"测试当前版本号大于服务端版本的异常场景\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.business\n    @pytest.mark.checkupdate\n    def test_version_rollback_scenario(self):\n        \"\"\"测试版本回退场景\"\"\"\n        base_headers = {\n            \"app-token\": \"valid-token-12345\",\n            \"platform\": \"android\",\n            \"version\": \"1.0.0\"\n        }\n    \n        # 测试版本号大于服务端最新版本的情况\n        future_version = self.latest_version + 10\n    \n        with allure.step(f\"测试未来版本{future_version}（大于服务端版本）\"):\n            response = self.client.get(\n                self.api_path,\n                headers=base_headers,\n                params={\"current_version\": future_version, \"os\": \"android\"}\n            )\n    \n            # 这种情况的处理取决于具体业务规则\n            # 可能返回200（不需要更新）或400（无效版本）\n            if response.status_code == 200:\n                json_data = ResponseValidator.validate_check_update_response(response)\n                # 未来版本应该不需要更新\n                assert json_data[\"data\"][\"new_update\"] == False, \\\n                    \"未来版本不应该提示更新\"\n                assert json_data[\"data\"][\"force_update\"] == False, \\\n                    \"未来版本不应该强制更新\"\n            else:\n                # 如果业务规则认为这是无效请求\n>               ResponseValidator.validate_status_code(response, 400)\n\ntests/business/test_check_update_business.py:230: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nresponse = <Response [404]>, expected_status = 400\n\n    @staticmethod\n    @allure.step(\"验证响应状态码\")\n    def validate_status_code(response, expected_status: int = 200):\n        \"\"\"验证响应状态码\"\"\"\n        actual_status = response.status_code\n        logger.info(f\"验证状态码: 期望={expected_status}, 实际={actual_status}\")\n    \n>       assert actual_status == expected_status, \\\n            f\"状态码不匹配: 期望={expected_status}, 实际={actual_status}\"\nE       AssertionError: 状态码不匹配: 期望=400, 实际=404\n\nutils/response_validator.py:50: AssertionError"}, "description": "测试当前版本号大于服务端版本的异常场景", "steps": [{"name": "测试未来版本110（大于服务端版本）", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=404\n", "trace": "  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/tests/business/test_check_update_business.py\", line 230, in test_version_rollback_scenario\n    ResponseValidator.validate_status_code(response, 400)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/api/v1/app/version/checkUpdate", "status": "passed", "attachments": [{"name": "请求URL", "source": "7cc1bf11-b012-4560-9da7-bf7ef103c5fb-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "74bd310a-859e-4a70-b979-b5fd3b91a19a-attachment.json", "type": "application/json"}], "start": 1750918184352, "stop": 1750918184352}], "attachments": [{"name": "响应信息", "source": "b8e57815-2fbc-44a5-bdda-45493f996565-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/api/v1/app/version/checkUpdate'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 110, 'os': 'android'}"}], "start": 1750918184352, "stop": 1750918185390}, {"name": "验证响应状态码", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=400, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "400"}], "start": 1750918185390, "stop": 1750918185390}], "start": 1750918184352, "stop": 1750918185391}], "attachments": [{"name": "stdout", "source": "4f8288c7-f852-49d2-a471-fac2fcb314ee-attachment.txt", "type": "text/plain"}], "start": 1750918184352, "stop": 1750918185391, "uuid": "b909df95-846b-4ce5-849d-119171305482", "historyId": "0211c4b401e7aea80a6bc80b481b198f", "testCaseId": "0211c4b401e7aea80a6bc80b481b198f", "fullName": "tests.business.test_check_update_business.TestCheckUpdateBusiness#test_version_rollback_scenario", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "checkUpdate业务逻辑测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "business"}, {"name": "parentSuite", "value": "tests.business"}, {"name": "suite", "value": "test_check_update_business"}, {"name": "subSuite", "value": "TestCheckUpdateBusiness"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.business.test_check_update_business"}]}