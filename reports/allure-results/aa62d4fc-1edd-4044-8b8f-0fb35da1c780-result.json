{"name": "新版本提示逻辑测试", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404", "trace": "self = <tests.business.test_check_update_business.TestCheckUpdateBusiness object at 0x10526b880>\n\n    @allure.title(\"新版本提示逻辑测试\")\n    @allure.description(\"测试新版本提示的触发条件\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.business\n    @pytest.mark.checkupdate\n    def test_new_update_logic(self):\n        \"\"\"测试新版本提示逻辑\"\"\"\n        base_headers = {\n            \"app-token\": \"valid-token-12345\",\n            \"platform\": \"android\",\n            \"version\": \"1.0.0\"\n        }\n    \n        # 测试刚好达到新版本提示阈值\n        threshold_version = self.latest_version - self.new_threshold\n    \n        with allure.step(f\"测试版本{threshold_version}（刚好达到新版本提示阈值）\"):\n            response = self.client.get(\n                self.api_path,\n                headers=base_headers,\n                params={\"current_version\": threshold_version, \"os\": \"android\"}\n            )\n    \n>           json_data = ResponseValidator.validate_check_update_response(response)\n\ntests/business/test_check_update_business.py:137: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \nutils/response_validator.py:152: in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nresponse = <Response [404]>, expected_status = 200\n\n    @staticmethod\n    @allure.step(\"验证响应状态码\")\n    def validate_status_code(response, expected_status: int = 200):\n        \"\"\"验证响应状态码\"\"\"\n        actual_status = response.status_code\n        logger.info(f\"验证状态码: 期望={expected_status}, 实际={actual_status}\")\n    \n>       assert actual_status == expected_status, \\\n            f\"状态码不匹配: 期望={expected_status}, 实际={actual_status}\"\nE       AssertionError: 状态码不匹配: 期望=200, 实际=404\n\nutils/response_validator.py:50: AssertionError"}, "description": "测试新版本提示的触发条件", "steps": [{"name": "测试版本99（刚好达到新版本提示阈值）", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/tests/business/test_check_update_business.py\", line 137, in test_new_update_logic\n    json_data = ResponseValidator.validate_check_update_response(response)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/api/v1/app/version/checkUpdate", "status": "passed", "attachments": [{"name": "请求URL", "source": "04556c4b-c790-4614-b635-9e1b34e87482-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "88a320f3-4d35-4bd4-abe2-51ed12d13710-attachment.json", "type": "application/json"}], "start": 1750918182846, "stop": 1750918182846}], "attachments": [{"name": "响应信息", "source": "ad2153c9-9e83-4110-ad98-5d58f8a9e25d-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/api/v1/app/version/checkUpdate'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 99, 'os': 'android'}"}], "start": 1750918182846, "stop": 1750918183157}, {"name": "完整验证checkUpdate响应", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 152, in validate_check_update_response\n    cls.validate_status_code(response, expected_status)\n  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "steps": [{"name": "验证响应状态码", "status": "failed", "statusDetails": {"message": "AssertionError: 状态码不匹配: 期望=200, 实际=404\n", "trace": "  File \"/opt/miniconda3/envs/testing/lib/python3.9/site-packages/allure_commons/_allure.py\", line 192, in impl\n    return func(*a, **kw)\n  File \"/Users/<USER>/Documents/augment-projects/Testing_Europ/utils/response_validator.py\", line 50, in validate_status_code\n    assert actual_status == expected_status, \\\n"}, "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}], "start": 1750918183158, "stop": 1750918183158}], "parameters": [{"name": "response", "value": "<Response [404]>"}, {"name": "expected_status", "value": "200"}, {"name": "expected_values", "value": "None"}, {"name": "max_response_time", "value": "5.0"}], "start": 1750918183157, "stop": 1750918183158}], "start": 1750918182846, "stop": 1750918183158}], "attachments": [{"name": "stdout", "source": "19262233-a4b0-4ecc-8e7a-6640465ece89-attachment.txt", "type": "text/plain"}], "start": 1750918182846, "stop": 1750918183158, "uuid": "21a828f4-78d3-40b0-a2e5-3fdb8396ba39", "historyId": "387c9d5983720d5f870d5202d2fd19f7", "testCaseId": "387c9d5983720d5f870d5202d2fd19f7", "fullName": "tests.business.test_check_update_business.TestCheckUpdateBusiness#test_new_update_logic", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "checkUpdate业务逻辑测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "business"}, {"name": "parentSuite", "value": "tests.business"}, {"name": "suite", "value": "test_check_update_business"}, {"name": "subSuite", "value": "TestCheckUpdateBusiness"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.business.test_check_update_business"}]}