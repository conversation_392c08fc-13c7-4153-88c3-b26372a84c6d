{"name": "不同HTTP方法测试", "status": "passed", "description": "测试使用错误HTTP方法访问接口", "steps": [{"name": "使用PUT方法请求", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "PUT https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "2ea3eea2-0d45-43df-bc67-17ab03b9db99-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "8651fc0b-651d-43f4-92af-fa72859bceab-attachment.json", "type": "application/json"}], "start": 1750918178821, "stop": 1750918178822}], "attachments": [{"name": "响应信息", "source": "e1fb9085-5cf2-445b-8efc-ab1a13c64d2a-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'PUT'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'android', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 61, 'os': 'android'}"}], "start": 1750918178821, "stop": 1750918179443}], "start": 1750918178821, "stop": 1750918179443}, {"name": "验证方法不允许错误", "status": "passed", "start": 1750918179443, "stop": 1750918179443}], "attachments": [{"name": "stdout", "source": "dc5d2b6a-41f9-42c2-b855-a18cfa091b52-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "method", "value": "'PUT'"}], "start": 1750918178821, "stop": 1750918179443, "uuid": "99b948c4-1c13-4edf-8544-ca1de9e63cf6", "historyId": "808e8e9417f4aa21d93cb2c25624c63e", "testCaseId": "c3832e1226d756e1e251bfcf7c9c5aa5", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_wrong_http_methods", "labels": [{"name": "severity", "value": "minor"}, {"name": "epic", "value": "充电APP后端接口测试"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}