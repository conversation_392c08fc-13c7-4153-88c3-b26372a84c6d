{"name": "并发请求测试", "status": "passed", "description": "测试接口在并发请求下的表现", "steps": [{"name": "发送10个并发请求", "status": "passed", "steps": [{"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "3375a598-ad8d-4522-9347-5d5ac249f31f-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "79438b8e-071b-40ba-aaa8-bb43b778cfc3-attachment.json", "type": "application/json"}], "start": 1750918176150, "stop": 1750918176150}], "attachments": [{"name": "响应信息", "source": "729eccc8-f782-470c-9350-b9f867860740-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "b918e205-f665-4fd2-912e-f54d88f998cb-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176148, "stop": 1750918176413}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "5ec9eac8-3c0b-4784-8904-aaee6277e3ae-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "c2948f0a-0702-47cb-94ee-b20a37ab6318-attachment.json", "type": "application/json"}], "start": 1750918176150, "stop": 1750918176151}], "attachments": [{"name": "响应信息", "source": "c9dbcb13-1da6-4e91-a3e6-d49a38de2cf5-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "6dd91e35-d7b2-43b2-b2fa-f8988f76e566-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176148, "stop": 1750918177728}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "f2a21211-dd28-44eb-8772-f44280d590d9-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "1d5bf32e-9092-4717-81a6-c9537938cada-attachment.json", "type": "application/json"}], "start": 1750918176150, "stop": 1750918176151}], "attachments": [{"name": "响应信息", "source": "bc1cad14-9e4c-40b1-9366-f23d5b3f21e5-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "f0294c89-7664-4438-a0e8-9feb2079dfd6-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176149, "stop": 1750918177594}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "12370814-b668-4388-a284-f182e781b4c5-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "bc4a5698-184e-4306-aa67-f64a1e0e90a0-attachment.json", "type": "application/json"}], "start": 1750918176151, "stop": 1750918176153}], "attachments": [{"name": "响应信息", "source": "18379610-3bcf-4e96-9579-dd11cd2651a6-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "924a66fd-b429-4cd8-ab21-0902913bab2e-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176149, "stop": 1750918177975}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "3fc387d3-deb2-4025-8be9-b024a1fc4c6d-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "adfe921f-afe5-464e-b0be-4e03b5d6020b-attachment.json", "type": "application/json"}], "start": 1750918176153, "stop": 1750918176157}], "attachments": [{"name": "响应信息", "source": "d1847aa1-9cae-465f-b531-47233318c392-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "46ffb1ad-c853-4dfd-92b9-cd3c10808b23-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176149, "stop": 1750918177741}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "16f1ba61-0d4d-4a1d-a0bd-fb640da8ed84-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "42ee840b-0acc-4c2f-90b1-c0832aaf3b1f-attachment.json", "type": "application/json"}], "start": 1750918176152, "stop": 1750918176155}], "attachments": [{"name": "响应信息", "source": "25d7c4c0-6762-4d65-9a00-f36609a53fb2-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "da361ea4-1c62-4778-af9c-65e3591aed3b-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176149, "stop": 1750918178064}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "f49b638a-3612-469b-9dd4-e265ce4fd009-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "500dc02f-986f-4b1b-972b-7953650948b1-attachment.json", "type": "application/json"}], "start": 1750918176152, "stop": 1750918176154}], "attachments": [{"name": "响应信息", "source": "7fe11444-81b6-4e6c-b776-c7ecda67b5a4-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "f4a70662-ecb5-4ed5-a29d-2932208f7772-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176149, "stop": 1750918177734}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "666d3ded-c483-4c59-a0b1-90fc2b5201f2-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "12168da7-e956-4d0e-8803-176346e544d4-attachment.json", "type": "application/json"}], "start": 1750918176152, "stop": 1750918176155}], "attachments": [{"name": "响应信息", "source": "b76fcd88-94b1-4cee-992f-ddd280113475-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "8c082a5a-82e8-4739-9117-5a0d73c2fb60-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176149, "stop": 1750918177735}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "6492e912-d022-4c43-a9ba-4d4294596428-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "4afee01a-e76f-49d4-bdb0-4315527f91c7-attachment.json", "type": "application/json"}], "start": 1750918176153, "stop": 1750918176157}], "attachments": [{"name": "响应信息", "source": "f033ed4f-ba13-4fde-a09b-ba49cbb3d10c-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "a49bd2ec-947e-45a2-81e3-b6ecb15e9488-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176149, "stop": 1750918177692}, {"name": "发送HTTP请求", "status": "passed", "steps": [{"name": "GET https://httpbin.org/get", "status": "passed", "attachments": [{"name": "请求URL", "source": "939e0577-b20a-439b-a800-36515d12d5f0-attachment.txt", "type": "text/plain"}, {"name": "请求参数", "source": "903b0319-41ff-463f-86d1-3f1740054f91-attachment.json", "type": "application/json"}], "start": 1750918176152, "stop": 1750918176153}], "attachments": [{"name": "响应信息", "source": "87908946-297d-42ba-8db2-3e2289cb97f7-attachment.txt", "type": "text/plain"}, {"name": "响应体", "source": "e9b38c90-04b0-4115-83ca-dd4d7ba40380-attachment.json", "type": "application/json"}], "parameters": [{"name": "method", "value": "'GET'"}, {"name": "path", "value": "'/get'"}, {"name": "headers", "value": "{'app-token': 'valid-token-12345', 'platform': 'ios', 'version': '1.0.0'}"}, {"name": "params", "value": "{'current_version': 38, 'os': 'ios'}"}], "start": 1750918176149, "stop": 1750918177742}], "start": 1750918176148, "stop": 1750918178065}, {"name": "验证并发请求结果", "status": "passed", "attachments": [{"name": "并发测试结果", "source": "8b94fbe5-8e47-48fb-bbf7-ec7f18606202-attachment.txt", "type": "text/plain"}], "start": 1750918178065, "stop": 1750918178065}], "attachments": [{"name": "stdout", "source": "ac50e356-e6b9-4d53-ad03-1b3877941f80-attachment.txt", "type": "text/plain"}], "start": 1750918176148, "stop": 1750918178066, "uuid": "40c058a2-7217-46ea-894a-9af3a4eeeb7d", "historyId": "e53a6b19ca701bd912e2ee092549bf5f", "testCaseId": "e53a6b19ca701bd912e2ee092549bf5f", "fullName": "tests.api.test_check_update_api.TestCheckUpdateAPI#test_concurrent_requests", "labels": [{"name": "epic", "value": "充电APP后端接口测试"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "checkUpdate接口API测试"}, {"name": "feature", "value": "版本检查"}, {"name": "tag", "value": "slow"}, {"name": "tag", "value": "checkupdate"}, {"name": "tag", "value": "api"}, {"name": "parentSuite", "value": "tests.api"}, {"name": "suite", "value": "test_check_update_api"}, {"name": "subSuite", "value": "TestCheckUpdateAPI"}, {"name": "host", "value": "WangyjdeMacBook-Pro.local"}, {"name": "thread", "value": "83285-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.api.test_check_update_api"}]}