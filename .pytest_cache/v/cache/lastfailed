{"tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_headers[test_case0]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_headers[test_case1]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_headers[test_case2]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_headers[test_case3]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_params[test_case0]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_params[test_case1]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_params[test_case2]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_params[test_case3]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_params[test_case4]": true, "tests/api/test_check_update_api.py::TestCheckUpdateAPI::test_invalid_params[test_case5]": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_version_update_scenarios[scenario0]": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_version_update_scenarios[scenario1]": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_version_update_scenarios[scenario2]": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_version_update_scenarios[scenario3]": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_force_update_logic": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_new_update_logic": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_platform_consistency[android-android]": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_platform_consistency[ios-ios]": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_version_rollback_scenario": true, "tests/business/test_check_update_business.py::TestCheckUpdateBusiness::test_business_rule_boundaries": true}