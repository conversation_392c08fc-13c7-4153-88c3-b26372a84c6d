

## 检测是否需要更新


**接口地址**:`/api/v1/app/version/checkUpdate`


**请求方式**:`GET`


**请求数据类型**:`*`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | in    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|app-token|令牌|header|true|string||
|current_version|current_version|query|true|integer(int32)||
|os|os|query|true|string||
|platform|平台|header|true|string||
|version|版本号|header|true|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|SimpleResponse«VersionUpdateVo»|
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|data||VersionUpdateVo|VersionUpdateVo|
|&emsp;&emsp;force_update||boolean||
|&emsp;&emsp;new_update||boolean||
|message|返回消息,成功为success|string||
|status|返回状态,成功为200|integer(int32)|integer(int32)|


**响应示例**:
```javascript
{
	"data": {
		"force_update": true,
		"new_update": true
	},
	"message": "success",
	"status": 200
}
```