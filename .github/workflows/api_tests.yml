name: API自动化测试

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每天凌晨2点运行
    - cron: '0 2 * * *'

jobs:
  api-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10']
        test-env: [test]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: 设置测试环境
      run: |
        python allure_config.py
        mkdir -p logs reports/allure-results
    
    - name: 运行冒烟测试
      env:
        TEST_ENV: ${{ matrix.test-env }}
      run: |
        pytest -m smoke --alluredir=reports/allure-results --html=reports/smoke-report.html --self-contained-html -v
    
    - name: 运行API测试
      env:
        TEST_ENV: ${{ matrix.test-env }}
      run: |
        pytest -m api --alluredir=reports/allure-results --html=reports/api-report.html --self-contained-html -v
      continue-on-error: true
    
    - name: 运行业务逻辑测试
      env:
        TEST_ENV: ${{ matrix.test-env }}
      run: |
        pytest -m business --alluredir=reports/allure-results --html=reports/business-report.html --self-contained-html -v
      continue-on-error: true
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-reports-${{ matrix.python-version }}-${{ matrix.test-env }}
        path: |
          reports/
          logs/
    
    - name: 上传Allure结果
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: allure-results-${{ matrix.python-version }}-${{ matrix.test-env }}
        path: reports/allure-results/
    
    - name: 发布Allure报告
      uses: simple-elf/allure-report-action@master
      if: always()
      with:
        allure_results: reports/allure-results
        allure_report: allure-report
        gh_pages: gh-pages
        allure_history: allure-history
    
    - name: 测试结果通知
      if: failure()
      run: |
        echo "测试失败，请检查测试报告"
        # 这里可以添加钉钉、企业微信等通知逻辑
