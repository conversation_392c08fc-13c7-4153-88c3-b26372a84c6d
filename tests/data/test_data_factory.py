"""
测试数据工厂
用于生成各种测试场景的数据
"""
from typing import Dict, List, Any, Optional
from faker import Faker
import random

fake = Faker('zh_CN')

class CheckUpdateDataFactory:
    """checkUpdate接口测试数据工厂"""
    
    @staticmethod
    def valid_request_data() -> Dict[str, Any]:
        """生成有效的请求数据"""
        return {
            "headers": {
                "app-token": "valid-token-12345",
                "platform": random.choice(["android", "ios"]),
                "version": "1.0.0"
            },
            "params": {
                "current_version": random.randint(1, 100),
                "os": random.choice(["android", "ios"])
            }
        }
    
    @staticmethod
    def invalid_headers_data() -> List[Dict[str, Any]]:
        """生成无效请求头的测试数据"""
        base_headers = {
            "app-token": "valid-token-12345",
            "platform": "android",
            "version": "1.0.0"
        }
        
        invalid_cases = []
        
        # 缺少app-token
        headers = base_headers.copy()
        del headers["app-token"]
        invalid_cases.append({
            "headers": headers,
            "params": {"current_version": 1, "os": "android"},
            "expected_status": 401,
            "case_name": "缺少app-token"
        })
        
        # 无效的app-token
        headers = base_headers.copy()
        headers["app-token"] = ""
        invalid_cases.append({
            "headers": headers,
            "params": {"current_version": 1, "os": "android"},
            "expected_status": 401,
            "case_name": "app-token为空"
        })
        
        # 缺少platform
        headers = base_headers.copy()
        del headers["platform"]
        invalid_cases.append({
            "headers": headers,
            "params": {"current_version": 1, "os": "android"},
            "expected_status": 400,
            "case_name": "缺少platform"
        })
        
        # 无效的platform
        headers = base_headers.copy()
        headers["platform"] = "windows"
        invalid_cases.append({
            "headers": headers,
            "params": {"current_version": 1, "os": "android"},
            "expected_status": 400,
            "case_name": "不支持的platform"
        })
        
        return invalid_cases
    
    @staticmethod
    def invalid_params_data() -> List[Dict[str, Any]]:
        """生成无效请求参数的测试数据"""
        base_headers = {
            "app-token": "valid-token-12345",
            "platform": "android",
            "version": "1.0.0"
        }
        
        invalid_cases = []
        
        # 缺少current_version
        invalid_cases.append({
            "headers": base_headers,
            "params": {"os": "android"},
            "expected_status": 400,
            "case_name": "缺少current_version"
        })
        
        # current_version为负数
        invalid_cases.append({
            "headers": base_headers,
            "params": {"current_version": -1, "os": "android"},
            "expected_status": 400,
            "case_name": "current_version为负数"
        })
        
        # current_version为0
        invalid_cases.append({
            "headers": base_headers,
            "params": {"current_version": 0, "os": "android"},
            "expected_status": 400,
            "case_name": "current_version为0"
        })
        
        # current_version为字符串
        invalid_cases.append({
            "headers": base_headers,
            "params": {"current_version": "abc", "os": "android"},
            "expected_status": 400,
            "case_name": "current_version为字符串"
        })
        
        # 缺少os
        invalid_cases.append({
            "headers": base_headers,
            "params": {"current_version": 1},
            "expected_status": 400,
            "case_name": "缺少os参数"
        })
        
        # os为空字符串
        invalid_cases.append({
            "headers": base_headers,
            "params": {"current_version": 1, "os": ""},
            "expected_status": 400,
            "case_name": "os为空字符串"
        })
        
        return invalid_cases
    
    @staticmethod
    def boundary_version_data() -> List[Dict[str, Any]]:
        """生成边界值测试数据"""
        base_headers = {
            "app-token": "valid-token-12345",
            "platform": "android",
            "version": "1.0.0"
        }
        
        boundary_cases = []
        
        # 最小版本号
        boundary_cases.append({
            "headers": base_headers,
            "params": {"current_version": 1, "os": "android"},
            "case_name": "最小版本号"
        })
        
        # 最大版本号
        boundary_cases.append({
            "headers": base_headers,
            "params": {"current_version": 999, "os": "android"},
            "case_name": "最大版本号"
        })
        
        # 超大版本号
        boundary_cases.append({
            "headers": base_headers,
            "params": {"current_version": 99999, "os": "android"},
            "case_name": "超大版本号"
        })
        
        return boundary_cases
    
    @staticmethod
    def business_scenario_data() -> List[Dict[str, Any]]:
        """生成业务场景测试数据"""
        base_headers = {
            "app-token": "valid-token-12345",
            "platform": "android",
            "version": "1.0.0"
        }
        
        scenarios = []
        
        # 场景1: 当前版本是最新版本
        scenarios.append({
            "headers": base_headers,
            "params": {"current_version": 100, "os": "android"},
            "expected_result": {
                "force_update": False,
                "new_update": False
            },
            "case_name": "当前版本是最新版本",
            "description": "用户使用的是最新版本，不需要更新"
        })
        
        # 场景2: 有新版本但不强制更新
        scenarios.append({
            "headers": base_headers,
            "params": {"current_version": 98, "os": "android"},
            "expected_result": {
                "force_update": False,
                "new_update": True
            },
            "case_name": "有新版本但不强制更新",
            "description": "版本差距较小，提示更新但不强制"
        })
        
        # 场景3: 版本过旧需要强制更新
        scenarios.append({
            "headers": base_headers,
            "params": {"current_version": 90, "os": "android"},
            "expected_result": {
                "force_update": True,
                "new_update": True
            },
            "case_name": "版本过旧需要强制更新",
            "description": "版本差距过大，需要强制更新"
        })
        
        # 场景4: iOS平台测试
        ios_headers = base_headers.copy()
        ios_headers["platform"] = "ios"
        scenarios.append({
            "headers": ios_headers,
            "params": {"current_version": 95, "os": "ios"},
            "expected_result": {
                "force_update": False,
                "new_update": True
            },
            "case_name": "iOS平台版本检查",
            "description": "测试iOS平台的版本检查逻辑"
        })
        
        return scenarios

class TestDataManager:
    """测试数据管理器"""
    
    def __init__(self):
        self.check_update_factory = CheckUpdateDataFactory()
    
    def get_test_data(self, data_type: str, **kwargs) -> Any:
        """获取指定类型的测试数据"""
        if data_type == "valid_request":
            return self.check_update_factory.valid_request_data()
        elif data_type == "invalid_headers":
            return self.check_update_factory.invalid_headers_data()
        elif data_type == "invalid_params":
            return self.check_update_factory.invalid_params_data()
        elif data_type == "boundary_version":
            return self.check_update_factory.boundary_version_data()
        elif data_type == "business_scenario":
            return self.check_update_factory.business_scenario_data()
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")

# 全局测试数据管理器实例
test_data_manager = TestDataManager()
