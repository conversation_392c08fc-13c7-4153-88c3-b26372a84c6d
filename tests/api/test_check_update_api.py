"""
checkUpdate接口API测试
测试接口的基本功能、参数验证、错误处理等
"""
import pytest
import allure
from tests.data.test_data_factory import test_data_manager
from utils.response_validator import ResponseValidator
from utils.logger import setup_logger

logger = setup_logger()

@allure.epic("充电APP后端接口测试")
@allure.feature("版本检查")
@allure.story("checkUpdate接口API测试")
class TestCheckUpdateAPI:
    """checkUpdate接口API测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, http_client):
        """测试方法前置设置"""
        self.client = http_client
        self.api_path = "/api/v1/app/version/checkUpdate"
    
    @allure.title("正常请求测试")
    @allure.description("测试使用有效参数请求checkUpdate接口")
    @allure.severity(allure.severity_level.BLOCKER)
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.checkupdate
    def test_valid_request(self):
        """测试正常请求"""
        # 获取测试数据
        test_data = test_data_manager.get_test_data("valid_request")
        
        with allure.step("发送checkUpdate请求"):
            response = self.client.get(
                self.api_path,
                headers=test_data["headers"],
                params=test_data["params"]
            )
        
        with allure.step("验证响应"):
            ResponseValidator.validate_check_update_response(response)
    
    @allure.title("无效请求头测试")
    @allure.description("测试使用无效请求头的情况")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.api
    @pytest.mark.checkupdate
    @pytest.mark.parametrize("test_case", test_data_manager.get_test_data("invalid_headers"))
    def test_invalid_headers(self, test_case):
        """测试无效请求头"""
        with allure.step(f"测试场景: {test_case['case_name']}"):
            response = self.client.get(
                self.api_path,
                headers=test_case["headers"],
                params=test_case["params"]
            )
        
        with allure.step("验证错误响应"):
            ResponseValidator.validate_status_code(
                response, 
                test_case["expected_status"]
            )
    
    @allure.title("无效请求参数测试")
    @allure.description("测试使用无效请求参数的情况")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.api
    @pytest.mark.checkupdate
    @pytest.mark.parametrize("test_case", test_data_manager.get_test_data("invalid_params"))
    def test_invalid_params(self, test_case):
        """测试无效请求参数"""
        with allure.step(f"测试场景: {test_case['case_name']}"):
            response = self.client.get(
                self.api_path,
                headers=test_case["headers"],
                params=test_case["params"]
            )
        
        with allure.step("验证错误响应"):
            ResponseValidator.validate_status_code(
                response, 
                test_case["expected_status"]
            )
    
    @allure.title("边界值测试")
    @allure.description("测试版本号边界值情况")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.api
    @pytest.mark.checkupdate
    @pytest.mark.parametrize("test_case", test_data_manager.get_test_data("boundary_version"))
    def test_boundary_values(self, test_case):
        """测试边界值"""
        with allure.step(f"测试场景: {test_case['case_name']}"):
            response = self.client.get(
                self.api_path,
                headers=test_case["headers"],
                params=test_case["params"]
            )
        
        with allure.step("验证响应"):
            # 边界值测试可能返回200或400，根据具体业务规则
            if response.status_code == 200:
                ResponseValidator.validate_check_update_response(response)
            else:
                ResponseValidator.validate_status_code(response, 400)
    
    @allure.title("响应时间测试")
    @allure.description("测试接口响应时间是否在合理范围内")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.api
    @pytest.mark.checkupdate
    def test_response_time(self):
        """测试响应时间"""
        test_data = test_data_manager.get_test_data("valid_request")
        
        with allure.step("发送请求并测量响应时间"):
            response = self.client.get(
                self.api_path,
                headers=test_data["headers"],
                params=test_data["params"]
            )
        
        with allure.step("验证响应时间"):
            ResponseValidator.validate_response_time(response, max_time=3.0)
    
    @allure.title("并发请求测试")
    @allure.description("测试接口在并发请求下的表现")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.api
    @pytest.mark.checkupdate
    @pytest.mark.slow
    def test_concurrent_requests(self):
        """测试并发请求"""
        import concurrent.futures
        import time
        
        test_data = test_data_manager.get_test_data("valid_request")
        
        def make_request():
            """发送单个请求"""
            response = self.client.get(
                self.api_path,
                headers=test_data["headers"],
                params=test_data["params"]
            )
            return response.status_code == 200
        
        with allure.step("发送10个并发请求"):
            start_time = time.time()
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(make_request) for _ in range(10)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            end_time = time.time()
        
        with allure.step("验证并发请求结果"):
            success_count = sum(results)
            total_time = end_time - start_time
            
            logger.info(f"并发测试结果: 成功{success_count}/10, 总耗时{total_time:.2f}秒")
            
            # 至少80%的请求应该成功
            assert success_count >= 8, f"并发请求成功率过低: {success_count}/10"
            
            # 总耗时应该合理（不应该是串行执行的时间）
            assert total_time < 10, f"并发请求总耗时过长: {total_time:.2f}秒"
            
            allure.attach(
                f"成功请求数: {success_count}/10\n"
                f"总耗时: {total_time:.2f}秒\n"
                f"平均耗时: {total_time/10:.2f}秒",
                "并发测试结果", allure.attachment_type.TEXT
            )
    
    @allure.title("不同HTTP方法测试")
    @allure.description("测试使用错误HTTP方法访问接口")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.api
    @pytest.mark.checkupdate
    @pytest.mark.parametrize("method", ["POST", "PUT", "DELETE", "PATCH"])
    def test_wrong_http_methods(self, method):
        """测试错误的HTTP方法"""
        test_data = test_data_manager.get_test_data("valid_request")
        
        with allure.step(f"使用{method}方法请求"):
            response = self.client.request(
                method,
                self.api_path,
                headers=test_data["headers"],
                params=test_data["params"]
            )
        
        with allure.step("验证方法不允许错误"):
            # 应该返回405 Method Not Allowed
            assert response.status_code in [405, 404], \
                f"期望405或404状态码，实际得到: {response.status_code}"
