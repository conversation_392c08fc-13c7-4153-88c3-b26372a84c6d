"""
checkUpdate接口业务逻辑测试
测试版本更新的业务规则和逻辑
"""
import pytest
import allure
from tests.data.test_data_factory import test_data_manager
from utils.response_validator import ResponseValidator, BusinessLogicValidator
from utils.logger import setup_logger

logger = setup_logger()

@allure.epic("充电APP后端接口测试")
@allure.feature("版本检查")
@allure.story("checkUpdate业务逻辑测试")
class TestCheckUpdateBusiness:
    """checkUpdate接口业务逻辑测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self, http_client, business_rules):
        """测试方法前置设置"""
        self.client = http_client
        self.api_path = "/api/v1/app/version/checkUpdate"
        self.business_rules = business_rules
        
        # 从配置中获取业务规则
        self.force_threshold = business_rules["version_update"]["force_update_threshold"]
        self.new_threshold = business_rules["version_update"]["new_update_threshold"]
        self.latest_version = 100  # 假设当前最新版本为100
    
    @allure.title("版本更新业务场景测试")
    @allure.description("测试不同版本差距下的更新提示逻辑")
    @allure.severity(allure.severity_level.BLOCKER)
    @pytest.mark.business
    @pytest.mark.checkupdate
    @pytest.mark.critical
    @pytest.mark.parametrize("scenario", test_data_manager.get_test_data("business_scenario"))
    def test_version_update_scenarios(self, scenario):
        """测试版本更新业务场景"""
        with allure.step(f"业务场景: {scenario['case_name']}"):
            allure.attach(scenario['description'], "场景描述", allure.attachment_type.TEXT)
            
            response = self.client.get(
                self.api_path,
                headers=scenario["headers"],
                params=scenario["params"]
            )
        
        with allure.step("验证响应格式"):
            json_data = ResponseValidator.validate_check_update_response(
                response, 
                expected_values=scenario.get("expected_result")
            )
        
        with allure.step("验证业务逻辑"):
            current_version = scenario["params"]["current_version"]
            actual_result = json_data["data"]
            
            BusinessLogicValidator.validate_version_update_logic(
                current_version=current_version,
                latest_version=self.latest_version,
                force_threshold=self.force_threshold,
                new_threshold=self.new_threshold,
                actual_result=actual_result
            )
    
    @allure.title("强制更新逻辑测试")
    @allure.description("测试强制更新的触发条件")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.business
    @pytest.mark.checkupdate
    def test_force_update_logic(self):
        """测试强制更新逻辑"""
        base_headers = {
            "app-token": "valid-token-12345",
            "platform": "android",
            "version": "1.0.0"
        }
        
        # 测试刚好达到强制更新阈值
        threshold_version = self.latest_version - self.force_threshold
        
        with allure.step(f"测试版本{threshold_version}（刚好达到强制更新阈值）"):
            response = self.client.get(
                self.api_path,
                headers=base_headers,
                params={"current_version": threshold_version, "os": "android"}
            )
            
            json_data = ResponseValidator.validate_check_update_response(response)
            
            # 刚好达到阈值应该触发强制更新
            assert json_data["data"]["force_update"] == True, \
                f"版本{threshold_version}应该触发强制更新"
            assert json_data["data"]["new_update"] == True, \
                f"版本{threshold_version}应该提示有新版本"
        
        # 测试刚好不达到强制更新阈值
        no_force_version = self.latest_version - self.force_threshold + 1
        
        with allure.step(f"测试版本{no_force_version}（刚好不达到强制更新阈值）"):
            response = self.client.get(
                self.api_path,
                headers=base_headers,
                params={"current_version": no_force_version, "os": "android"}
            )
            
            json_data = ResponseValidator.validate_check_update_response(response)
            
            # 不达到阈值不应该强制更新
            assert json_data["data"]["force_update"] == False, \
                f"版本{no_force_version}不应该触发强制更新"
    
    @allure.title("新版本提示逻辑测试")
    @allure.description("测试新版本提示的触发条件")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.business
    @pytest.mark.checkupdate
    def test_new_update_logic(self):
        """测试新版本提示逻辑"""
        base_headers = {
            "app-token": "valid-token-12345",
            "platform": "android",
            "version": "1.0.0"
        }
        
        # 测试刚好达到新版本提示阈值
        threshold_version = self.latest_version - self.new_threshold
        
        with allure.step(f"测试版本{threshold_version}（刚好达到新版本提示阈值）"):
            response = self.client.get(
                self.api_path,
                headers=base_headers,
                params={"current_version": threshold_version, "os": "android"}
            )
            
            json_data = ResponseValidator.validate_check_update_response(response)
            
            # 刚好达到阈值应该提示新版本
            assert json_data["data"]["new_update"] == True, \
                f"版本{threshold_version}应该提示有新版本"
        
        # 测试最新版本
        with allure.step(f"测试最新版本{self.latest_version}"):
            response = self.client.get(
                self.api_path,
                headers=base_headers,
                params={"current_version": self.latest_version, "os": "android"}
            )
            
            json_data = ResponseValidator.validate_check_update_response(response)
            
            # 最新版本不应该提示更新
            assert json_data["data"]["new_update"] == False, \
                f"最新版本{self.latest_version}不应该提示更新"
            assert json_data["data"]["force_update"] == False, \
                f"最新版本{self.latest_version}不应该强制更新"
    
    @allure.title("不同平台业务逻辑测试")
    @allure.description("测试Android和iOS平台的业务逻辑一致性")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.business
    @pytest.mark.checkupdate
    @pytest.mark.parametrize("platform,os", [("android", "android"), ("ios", "ios")])
    def test_platform_consistency(self, platform, os):
        """测试不同平台的业务逻辑一致性"""
        headers = {
            "app-token": "valid-token-12345",
            "platform": platform,
            "version": "1.0.0"
        }
        
        test_version = self.latest_version - 3  # 测试一个中等版本差距
        
        with allure.step(f"测试{platform}平台版本{test_version}"):
            response = self.client.get(
                self.api_path,
                headers=headers,
                params={"current_version": test_version, "os": os}
            )
            
            json_data = ResponseValidator.validate_check_update_response(response)
            
            # 验证业务逻辑
            version_diff = self.latest_version - test_version
            expected_force = version_diff >= self.force_threshold
            expected_new = version_diff >= self.new_threshold
            
            assert json_data["data"]["force_update"] == expected_force, \
                f"{platform}平台强制更新逻辑错误"
            assert json_data["data"]["new_update"] == expected_new, \
                f"{platform}平台新版本提示逻辑错误"
            
            logger.info(f"{platform}平台业务逻辑验证通过")
    
    @allure.title("版本回退场景测试")
    @allure.description("测试当前版本号大于服务端版本的异常场景")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.business
    @pytest.mark.checkupdate
    def test_version_rollback_scenario(self):
        """测试版本回退场景"""
        base_headers = {
            "app-token": "valid-token-12345",
            "platform": "android",
            "version": "1.0.0"
        }
        
        # 测试版本号大于服务端最新版本的情况
        future_version = self.latest_version + 10
        
        with allure.step(f"测试未来版本{future_version}（大于服务端版本）"):
            response = self.client.get(
                self.api_path,
                headers=base_headers,
                params={"current_version": future_version, "os": "android"}
            )
            
            # 这种情况的处理取决于具体业务规则
            # 可能返回200（不需要更新）或400（无效版本）
            if response.status_code == 200:
                json_data = ResponseValidator.validate_check_update_response(response)
                # 未来版本应该不需要更新
                assert json_data["data"]["new_update"] == False, \
                    "未来版本不应该提示更新"
                assert json_data["data"]["force_update"] == False, \
                    "未来版本不应该强制更新"
            else:
                # 如果业务规则认为这是无效请求
                ResponseValidator.validate_status_code(response, 400)
    
    @allure.title("业务规则边界测试")
    @allure.description("测试业务规则的边界条件")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.business
    @pytest.mark.checkupdate
    def test_business_rule_boundaries(self):
        """测试业务规则边界"""
        base_headers = {
            "app-token": "valid-token-12345",
            "platform": "android",
            "version": "1.0.0"
        }
        
        # 测试各种边界条件
        test_cases = [
            {
                "version": self.latest_version - self.force_threshold + 1,
                "expected_force": False,
                "expected_new": True,
                "description": "刚好不触发强制更新"
            },
            {
                "version": self.latest_version - self.force_threshold,
                "expected_force": True,
                "expected_new": True,
                "description": "刚好触发强制更新"
            },
            {
                "version": self.latest_version - self.new_threshold + 1,
                "expected_force": False,
                "expected_new": False,
                "description": "刚好不提示新版本"
            },
            {
                "version": self.latest_version - self.new_threshold,
                "expected_force": False,
                "expected_new": True,
                "description": "刚好提示新版本"
            }
        ]
        
        for case in test_cases:
            with allure.step(f"测试边界条件: {case['description']}"):
                response = self.client.get(
                    self.api_path,
                    headers=base_headers,
                    params={"current_version": case["version"], "os": "android"}
                )
                
                json_data = ResponseValidator.validate_check_update_response(response)
                
                assert json_data["data"]["force_update"] == case["expected_force"], \
                    f"版本{case['version']}强制更新逻辑错误: {case['description']}"
                assert json_data["data"]["new_update"] == case["expected_new"], \
                    f"版本{case['version']}新版本提示逻辑错误: {case['description']}"
                
                logger.info(f"边界条件验证通过: {case['description']}")
