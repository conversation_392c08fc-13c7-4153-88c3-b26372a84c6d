"""
响应验证工具
用于验证API响应的格式和内容
"""
from typing import Dict, Any, Optional, List
import json
from jsonschema import validate, ValidationError
import allure
from .logger import setup_logger

logger = setup_logger()

class ResponseValidator:
    """响应验证器"""
    
    # checkUpdate接口响应schema
    CHECK_UPDATE_SCHEMA = {
        "type": "object",
        "properties": {
            "status": {
                "type": "integer",
                "enum": [200]
            },
            "message": {
                "type": "string"
            },
            "data": {
                "type": "object",
                "properties": {
                    "force_update": {
                        "type": "boolean"
                    },
                    "new_update": {
                        "type": "boolean"
                    }
                },
                "required": ["force_update", "new_update"]
            }
        },
        "required": ["status", "message", "data"]
    }
    
    @staticmethod
    @allure.step("验证响应状态码")
    def validate_status_code(response, expected_status: int = 200):
        """验证响应状态码"""
        actual_status = response.status_code
        logger.info(f"验证状态码: 期望={expected_status}, 实际={actual_status}")
        
        assert actual_status == expected_status, \
            f"状态码不匹配: 期望={expected_status}, 实际={actual_status}"
        
        allure.attach(
            f"期望状态码: {expected_status}\n实际状态码: {actual_status}",
            "状态码验证", allure.attachment_type.TEXT
        )
    
    @staticmethod
    @allure.step("验证响应JSON格式")
    def validate_json_format(response) -> Dict[str, Any]:
        """验证响应是否为有效JSON格式"""
        try:
            json_data = response.json()
            logger.info("响应JSON格式验证通过")
            
            allure.attach(
                json.dumps(json_data, ensure_ascii=False, indent=2),
                "响应JSON", allure.attachment_type.JSON
            )
            
            return json_data
        except json.JSONDecodeError as e:
            logger.error(f"响应不是有效的JSON格式: {e}")
            allure.attach(response.text, "无效响应内容", allure.attachment_type.TEXT)
            raise AssertionError(f"响应不是有效的JSON格式: {e}")
    
    @staticmethod
    @allure.step("验证响应Schema")
    def validate_schema(json_data: Dict[str, Any], schema: Dict[str, Any]):
        """验证响应数据结构"""
        try:
            validate(instance=json_data, schema=schema)
            logger.info("响应Schema验证通过")
            
            allure.attach(
                json.dumps(schema, ensure_ascii=False, indent=2),
                "期望Schema", allure.attachment_type.JSON
            )
            
        except ValidationError as e:
            logger.error(f"响应Schema验证失败: {e.message}")
            allure.attach(
                f"验证失败路径: {' -> '.join(str(p) for p in e.path)}\n"
                f"错误信息: {e.message}",
                "Schema验证错误", allure.attachment_type.TEXT
            )
            raise AssertionError(f"响应Schema验证失败: {e.message}")
    
    @staticmethod
    @allure.step("验证业务字段")
    def validate_business_fields(json_data: Dict[str, Any], 
                                expected_values: Optional[Dict[str, Any]] = None):
        """验证业务相关字段"""
        data = json_data.get("data", {})
        
        # 验证必要字段存在
        required_fields = ["force_update", "new_update"]
        for field in required_fields:
            assert field in data, f"缺少必要字段: {field}"
            assert isinstance(data[field], bool), f"字段{field}应该是布尔类型"
        
        logger.info(f"业务字段验证通过: force_update={data['force_update']}, new_update={data['new_update']}")
        
        # 验证期望值
        if expected_values:
            for field, expected_value in expected_values.items():
                if field in data:
                    actual_value = data[field]
                    assert actual_value == expected_value, \
                        f"字段{field}值不匹配: 期望={expected_value}, 实际={actual_value}"
                    logger.info(f"字段{field}值验证通过: {actual_value}")
        
        allure.attach(
            f"force_update: {data['force_update']}\n"
            f"new_update: {data['new_update']}",
            "业务字段值", allure.attachment_type.TEXT
        )
    
    @staticmethod
    @allure.step("验证响应时间")
    def validate_response_time(response, max_time: float = 5.0):
        """验证响应时间"""
        response_time = response.elapsed.total_seconds()
        logger.info(f"响应时间验证: {response_time:.3f}秒 (最大允许: {max_time}秒)")
        
        assert response_time <= max_time, \
            f"响应时间过长: {response_time:.3f}秒 > {max_time}秒"
        
        allure.attach(
            f"响应时间: {response_time:.3f}秒\n最大允许: {max_time}秒",
            "响应时间验证", allure.attachment_type.TEXT
        )
    
    @classmethod
    @allure.step("完整验证checkUpdate响应")
    def validate_check_update_response(cls, response, 
                                     expected_status: int = 200,
                                     expected_values: Optional[Dict[str, Any]] = None,
                                     max_response_time: float = 5.0):
        """完整验证checkUpdate接口响应"""
        # 验证状态码
        cls.validate_status_code(response, expected_status)
        
        # 如果状态码不是200，不需要验证JSON内容
        if expected_status != 200:
            return
        
        # 验证JSON格式
        json_data = cls.validate_json_format(response)
        
        # 验证Schema
        cls.validate_schema(json_data, cls.CHECK_UPDATE_SCHEMA)
        
        # 验证业务字段
        cls.validate_business_fields(json_data, expected_values)
        
        # 验证响应时间
        cls.validate_response_time(response, max_response_time)
        
        logger.info("checkUpdate响应完整验证通过")
        return json_data

class BusinessLogicValidator:
    """业务逻辑验证器"""
    
    @staticmethod
    @allure.step("验证版本更新逻辑")
    def validate_version_update_logic(current_version: int, 
                                    latest_version: int,
                                    force_threshold: int,
                                    new_threshold: int,
                                    actual_result: Dict[str, bool]):
        """验证版本更新业务逻辑"""
        version_diff = latest_version - current_version
        
        # 计算期望结果
        expected_force_update = version_diff >= force_threshold
        expected_new_update = version_diff >= new_threshold
        
        logger.info(f"版本更新逻辑验证: 当前版本={current_version}, "
                   f"最新版本={latest_version}, 版本差距={version_diff}")
        logger.info(f"期望结果: force_update={expected_force_update}, "
                   f"new_update={expected_new_update}")
        logger.info(f"实际结果: force_update={actual_result['force_update']}, "
                   f"new_update={actual_result['new_update']}")
        
        # 验证强制更新逻辑
        assert actual_result['force_update'] == expected_force_update, \
            f"强制更新逻辑错误: 期望={expected_force_update}, 实际={actual_result['force_update']}"
        
        # 验证新版本提示逻辑
        assert actual_result['new_update'] == expected_new_update, \
            f"新版本提示逻辑错误: 期望={expected_new_update}, 实际={actual_result['new_update']}"
        
        allure.attach(
            f"当前版本: {current_version}\n"
            f"最新版本: {latest_version}\n"
            f"版本差距: {version_diff}\n"
            f"强制更新阈值: {force_threshold}\n"
            f"新版本提示阈值: {new_threshold}\n"
            f"期望强制更新: {expected_force_update}\n"
            f"期望新版本提示: {expected_new_update}\n"
            f"实际强制更新: {actual_result['force_update']}\n"
            f"实际新版本提示: {actual_result['new_update']}",
            "版本更新逻辑验证", allure.attachment_type.TEXT
        )
        
        logger.info("版本更新逻辑验证通过")
