"""
HTTP客户端工具类
封装requests库，提供统一的HTTP请求接口
"""
import json
import time
from typing import Dict, Any, Optional, Union
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import allure
from .logger import setup_logger

logger = setup_logger()

class HttpClient:
    """HTTP客户端类"""
    
    def __init__(self, base_url: str = "", timeout: int = 30, 
                 retry_times: int = 3, verify_ssl: bool = True):
        """
        初始化HTTP客户端
        
        Args:
            base_url: 基础URL
            timeout: 请求超时时间
            retry_times: 重试次数
            verify_ssl: 是否验证SSL证书
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self.session = requests.Session()
        self.default_headers = {}
        
        # 配置重试策略
        retry_strategy = Retry(
            total=retry_times,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        logger.info(f"HTTP客户端初始化完成: {base_url}")
    
    def set_default_headers(self, headers: Dict[str, str]):
        """设置默认请求头"""
        self.default_headers.update(headers)
        self.session.headers.update(headers)
        logger.debug(f"设置默认请求头: {headers}")
    
    def _build_url(self, path: str) -> str:
        """构建完整URL"""
        if path.startswith('http'):
            return path
        return f"{self.base_url}{path}" if path.startswith('/') else f"{self.base_url}/{path}"
    
    def _log_request(self, method: str, url: str, **kwargs):
        """记录请求日志"""
        logger.info(f"发送{method}请求: {url}")
        if kwargs.get('params'):
            logger.debug(f"请求参数: {kwargs['params']}")
        if kwargs.get('json'):
            logger.debug(f"请求体: {json.dumps(kwargs['json'], ensure_ascii=False, indent=2)}")
        if kwargs.get('headers'):
            # 隐藏敏感信息
            safe_headers = {k: v if 'token' not in k.lower() else '***' 
                          for k, v in kwargs['headers'].items()}
            logger.debug(f"请求头: {safe_headers}")
    
    def _log_response(self, response: requests.Response):
        """记录响应日志"""
        logger.info(f"响应状态码: {response.status_code}")
        logger.debug(f"响应头: {dict(response.headers)}")
        try:
            if response.headers.get('content-type', '').startswith('application/json'):
                logger.debug(f"响应体: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
            else:
                logger.debug(f"响应体: {response.text[:500]}...")
        except Exception as e:
            logger.warning(f"解析响应体失败: {e}")
    
    @allure.step("发送HTTP请求")
    def request(self, method: str, path: str, **kwargs) -> requests.Response:
        """
        发送HTTP请求
        
        Args:
            method: 请求方法
            path: 请求路径
            **kwargs: 其他请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        url = self._build_url(path)
        
        # 合并headers
        headers = self.default_headers.copy()
        if 'headers' in kwargs:
            headers.update(kwargs['headers'])
            kwargs['headers'] = headers
        else:
            kwargs['headers'] = headers
        
        # 设置默认参数
        kwargs.setdefault('timeout', self.timeout)
        kwargs.setdefault('verify', self.verify_ssl)
        
        # 记录请求
        self._log_request(method, url, **kwargs)
        
        # 添加到allure报告
        with allure.step(f"{method.upper()} {url}"):
            allure.attach(url, "请求URL", allure.attachment_type.TEXT)
            if kwargs.get('params'):
                allure.attach(
                    json.dumps(kwargs['params'], ensure_ascii=False, indent=2),
                    "请求参数", allure.attachment_type.JSON
                )
            if kwargs.get('json'):
                allure.attach(
                    json.dumps(kwargs['json'], ensure_ascii=False, indent=2),
                    "请求体", allure.attachment_type.JSON
                )
        
        start_time = time.time()
        try:
            response = self.session.request(method, url, **kwargs)
            duration = time.time() - start_time
            
            # 记录响应
            self._log_response(response)
            logger.info(f"请求耗时: {duration:.3f}秒")
            
            # 添加到allure报告
            allure.attach(
                f"状态码: {response.status_code}\n耗时: {duration:.3f}秒",
                "响应信息", allure.attachment_type.TEXT
            )
            
            try:
                if response.headers.get('content-type', '').startswith('application/json'):
                    allure.attach(
                        json.dumps(response.json(), ensure_ascii=False, indent=2),
                        "响应体", allure.attachment_type.JSON
                    )
            except:
                allure.attach(response.text, "响应体", allure.attachment_type.TEXT)
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"请求失败: {e}, 耗时: {duration:.3f}秒")
            allure.attach(str(e), "错误信息", allure.attachment_type.TEXT)
            raise
    
    def get(self, path: str, **kwargs) -> requests.Response:
        """GET请求"""
        return self.request('GET', path, **kwargs)
    
    def post(self, path: str, **kwargs) -> requests.Response:
        """POST请求"""
        return self.request('POST', path, **kwargs)
    
    def put(self, path: str, **kwargs) -> requests.Response:
        """PUT请求"""
        return self.request('PUT', path, **kwargs)
    
    def delete(self, path: str, **kwargs) -> requests.Response:
        """DELETE请求"""
        return self.request('DELETE', path, **kwargs)
    
    def close(self):
        """关闭会话"""
        self.session.close()
        logger.info("HTTP客户端会话已关闭")
