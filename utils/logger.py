"""
日志工具模块
"""
import sys
from pathlib import Path
from loguru import logger

def setup_logger():
    """设置日志配置"""
    # 移除默认handler
    logger.remove()
    
    # 控制台输出
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 文件输出
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # 详细日志文件
    logger.add(
        logs_dir / "test_detail.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB",
        retention="7 days",
        encoding="utf-8"
    )
    
    # 错误日志文件
    logger.add(
        logs_dir / "test_error.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="ERROR",
        rotation="10 MB",
        retention="30 days",
        encoding="utf-8"
    )
    
    return logger
