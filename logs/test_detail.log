2025-06-26 14:09:01 | INFO     | conftest:config_data:61 - 加载配置完成，当前环境: test
2025-06-26 14:09:01 | INFO     | utils.http_client:__init__:48 - HTTP客户端初始化完成: https://httpbin.org
2025-06-26 14:09:01 | DEBUG    | utils.http_client:set_default_headers:54 - 设置默认请求头: {'app-token': 'your-app-token-here', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:01 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_valid_request
2025-06-26 14:09:01 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:01 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 98, 'os': 'ios'}
2025-06-26 14:09:01 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:03 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:03 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:03 GMT', 'Content-Type': 'application/json', 'Content-Length': '520', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:03 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "98",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce3ff-5cc75eae345b13563c7d950f"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=98&os=ios"
}
2025-06-26 14:09:03 | INFO     | utils.http_client:request:138 - 请求耗时: 1.138秒
2025-06-26 14:09:03 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=200
2025-06-26 14:09:03 | INFO     | utils.response_validator:validate_json_format:64 - 响应JSON格式验证通过
2025-06-26 14:09:03 | INFO     | utils.response_validator:validate_check_update_response:164 - httpbin.org响应格式验证通过
2025-06-26 14:09:03 | INFO     | utils.response_validator:validate_response_time:134 - 响应时间验证: 1.134秒 (最大允许: 5.0秒)
2025-06-26 14:09:03 | INFO     | utils.response_validator:validate_check_update_response:169 - 演示版本响应验证通过
2025-06-26 14:09:03 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_valid_request
2025-06-26 14:09:03 | INFO     | utils.http_client:close:182 - HTTP客户端会话已关闭
2025-06-26 14:09:29 | INFO     | conftest:config_data:61 - 加载配置完成，当前环境: test
2025-06-26 14:09:29 | INFO     | utils.http_client:__init__:48 - HTTP客户端初始化完成: https://httpbin.org
2025-06-26 14:09:29 | DEBUG    | utils.http_client:set_default_headers:54 - 设置默认请求头: {'app-token': 'your-app-token-here', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:29 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_valid_request
2025-06-26 14:09:29 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:29 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 23, 'os': 'ios'}
2025-06-26 14:09:29 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:30 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:30 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '520', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:30 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "23",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41a-68c07a6a6515b28854eee681"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=23&os=ios"
}
2025-06-26 14:09:30 | INFO     | utils.http_client:request:138 - 请求耗时: 1.157秒
2025-06-26 14:09:30 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=200
2025-06-26 14:09:30 | INFO     | utils.response_validator:validate_json_format:64 - 响应JSON格式验证通过
2025-06-26 14:09:30 | INFO     | utils.response_validator:validate_check_update_response:164 - httpbin.org响应格式验证通过
2025-06-26 14:09:30 | INFO     | utils.response_validator:validate_response_time:134 - 响应时间验证: 1.153秒 (最大允许: 5.0秒)
2025-06-26 14:09:30 | INFO     | utils.response_validator:validate_check_update_response:169 - 演示版本响应验证通过
2025-06-26 14:09:30 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_valid_request
2025-06-26 14:09:30 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_headers[test_case0]
2025-06-26 14:09:30 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:30 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 1, 'os': 'android'}
2025-06-26 14:09:30 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:30 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:30 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '528', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:30 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "1",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "your-app-token-here",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41a-0ba3c86d05a7b6f0569bd4c9"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=1&os=android"
}
2025-06-26 14:09:30 | INFO     | utils.http_client:request:138 - 请求耗时: 0.470秒
2025-06-26 14:09:30 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=401, 实际=200
2025-06-26 14:09:30 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_headers[test_case0]
2025-06-26 14:09:30 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_headers[test_case1]
2025-06-26 14:09:30 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:30 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 1, 'os': 'android'}
2025-06-26 14:09:30 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:31 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:31 GMT', 'Content-Type': 'application/json', 'Content-Length': '509', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "1",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41b-69d0c5f27195e602133dbcfb"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=1&os=android"
}
2025-06-26 14:09:31 | INFO     | utils.http_client:request:138 - 请求耗时: 0.277秒
2025-06-26 14:09:31 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=401, 实际=200
2025-06-26 14:09:31 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_headers[test_case1]
2025-06-26 14:09:31 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_headers[test_case2]
2025-06-26 14:09:31 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 1, 'os': 'android'}
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:31 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:31 GMT', 'Content-Type': 'application/json', 'Content-Length': '526', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "1",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41b-1d0cfb99179c80776492af1b"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=1&os=android"
}
2025-06-26 14:09:31 | INFO     | utils.http_client:request:138 - 请求耗时: 0.268秒
2025-06-26 14:09:31 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=200
2025-06-26 14:09:31 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_headers[test_case2]
2025-06-26 14:09:31 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_headers[test_case3]
2025-06-26 14:09:31 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 1, 'os': 'android'}
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'windows', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:31 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:31 GMT', 'Content-Type': 'application/json', 'Content-Length': '526', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "1",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "windows",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41b-318cd33e542a516a5aa362ef"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=1&os=android"
}
2025-06-26 14:09:31 | INFO     | utils.http_client:request:138 - 请求耗时: 0.263秒
2025-06-26 14:09:31 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=200
2025-06-26 14:09:31 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_headers[test_case3]
2025-06-26 14:09:31 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_params[test_case0]
2025-06-26 14:09:31 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'os': 'android'}
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:31 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:31 GMT', 'Content-Type': 'application/json', 'Content-Length': '479', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41b-7fc3ebae70a863383e9d883e"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?os=android"
}
2025-06-26 14:09:31 | INFO     | utils.http_client:request:138 - 请求耗时: 0.284秒
2025-06-26 14:09:31 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=200
2025-06-26 14:09:31 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_params[test_case0]
2025-06-26 14:09:31 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_params[test_case1]
2025-06-26 14:09:31 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': -1, 'os': 'android'}
2025-06-26 14:09:31 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:32 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:32 GMT', 'Content-Type': 'application/json', 'Content-Length': '528', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "-1",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41c-3c513b05047147c66b4b8d82"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=-1&os=android"
}
2025-06-26 14:09:32 | INFO     | utils.http_client:request:138 - 请求耗时: 0.261秒
2025-06-26 14:09:32 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=200
2025-06-26 14:09:32 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_params[test_case1]
2025-06-26 14:09:32 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_params[test_case2]
2025-06-26 14:09:32 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 0, 'os': 'android'}
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:32 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:32 GMT', 'Content-Type': 'application/json', 'Content-Length': '526', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "0",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41c-68976a7f7323343a2e630061"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=0&os=android"
}
2025-06-26 14:09:32 | INFO     | utils.http_client:request:138 - 请求耗时: 0.266秒
2025-06-26 14:09:32 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=200
2025-06-26 14:09:32 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_params[test_case2]
2025-06-26 14:09:32 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_params[test_case3]
2025-06-26 14:09:32 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 'abc', 'os': 'android'}
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:32 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:32 GMT', 'Content-Type': 'application/json', 'Content-Length': '530', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "abc",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41c-06963df65d9e929f773e3e51"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=abc&os=android"
}
2025-06-26 14:09:32 | INFO     | utils.http_client:request:138 - 请求耗时: 0.358秒
2025-06-26 14:09:32 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=200
2025-06-26 14:09:32 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_params[test_case3]
2025-06-26 14:09:32 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_params[test_case4]
2025-06-26 14:09:32 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 1}
2025-06-26 14:09:32 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:34 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:34 GMT', 'Content-Type': 'application/json', 'Content-Length': '493', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "1"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41d-12a31e6f693f2af73f439bb8"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=1"
}
2025-06-26 14:09:34 | INFO     | utils.http_client:request:138 - 请求耗时: 1.468秒
2025-06-26 14:09:34 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=200
2025-06-26 14:09:34 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_params[test_case4]
2025-06-26 14:09:34 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_invalid_params[test_case5]
2025-06-26 14:09:34 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 1, 'os': ''}
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:34 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:34 GMT', 'Content-Type': 'application/json', 'Content-Length': '512', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "1",
    "os": ""
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41e-555b027a736f6abb76df5d75"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=1&os="
}
2025-06-26 14:09:34 | INFO     | utils.http_client:request:138 - 请求耗时: 0.262秒
2025-06-26 14:09:34 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=200
2025-06-26 14:09:34 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_invalid_params[test_case5]
2025-06-26 14:09:34 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_boundary_values[test_case0]
2025-06-26 14:09:34 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 1, 'os': 'android'}
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:34 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:34 GMT', 'Content-Type': 'application/json', 'Content-Length': '526', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "1",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41e-445f446b01f70a6d27e07212"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=1&os=android"
}
2025-06-26 14:09:34 | INFO     | utils.http_client:request:138 - 请求耗时: 0.264秒
2025-06-26 14:09:34 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=200
2025-06-26 14:09:34 | INFO     | utils.response_validator:validate_json_format:64 - 响应JSON格式验证通过
2025-06-26 14:09:34 | INFO     | utils.response_validator:validate_check_update_response:164 - httpbin.org响应格式验证通过
2025-06-26 14:09:34 | INFO     | utils.response_validator:validate_response_time:134 - 响应时间验证: 0.263秒 (最大允许: 5.0秒)
2025-06-26 14:09:34 | INFO     | utils.response_validator:validate_check_update_response:169 - 演示版本响应验证通过
2025-06-26 14:09:34 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_boundary_values[test_case0]
2025-06-26 14:09:34 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_boundary_values[test_case1]
2025-06-26 14:09:34 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 999, 'os': 'android'}
2025-06-26 14:09:34 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:35 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:35 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:35 GMT', 'Content-Type': 'application/json', 'Content-Length': '530', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:35 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "999",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41f-36e9178e2ec9b55450c9faa9"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=999&os=android"
}
2025-06-26 14:09:35 | INFO     | utils.http_client:request:138 - 请求耗时: 0.262秒
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=200
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_json_format:64 - 响应JSON格式验证通过
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_check_update_response:164 - httpbin.org响应格式验证通过
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_response_time:134 - 响应时间验证: 0.262秒 (最大允许: 5.0秒)
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_check_update_response:169 - 演示版本响应验证通过
2025-06-26 14:09:35 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_boundary_values[test_case1]
2025-06-26 14:09:35 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_boundary_values[test_case2]
2025-06-26 14:09:35 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:35 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 99999, 'os': 'android'}
2025-06-26 14:09:35 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:35 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:35 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:35 GMT', 'Content-Type': 'application/json', 'Content-Length': '534', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:35 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "99999",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41f-107b786c3bf9676d39e50097"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=99999&os=android"
}
2025-06-26 14:09:35 | INFO     | utils.http_client:request:138 - 请求耗时: 0.270秒
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=200
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_json_format:64 - 响应JSON格式验证通过
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_check_update_response:164 - httpbin.org响应格式验证通过
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_response_time:134 - 响应时间验证: 0.269秒 (最大允许: 5.0秒)
2025-06-26 14:09:35 | INFO     | utils.response_validator:validate_check_update_response:169 - 演示版本响应验证通过
2025-06-26 14:09:35 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_boundary_values[test_case2]
2025-06-26 14:09:35 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_response_time
2025-06-26 14:09:35 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:35 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 35, 'os': 'android'}
2025-06-26 14:09:35 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:36 GMT', 'Content-Type': 'application/json', 'Content-Length': '528', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "35",
    "os": "android"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "android",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce41f-2245261000d8f71c25b712ed"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=35&os=android"
}
2025-06-26 14:09:36 | INFO     | utils.http_client:request:138 - 请求耗时: 0.692秒
2025-06-26 14:09:36 | INFO     | utils.response_validator:validate_response_time:134 - 响应时间验证: 0.691秒 (最大允许: 3.0秒)
2025-06-26 14:09:36 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_response_time
2025-06-26 14:09:36 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_concurrent_requests
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 38, 'os': 'ios'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:36 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:36 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:36 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce420-62e15af3367ab01875278dc4"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:36 | INFO     | utils.http_client:request:138 - 请求耗时: 0.262秒
2025-06-26 14:09:37 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-55d547ef4022a7cb47784904"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:37 | INFO     | utils.http_client:request:138 - 请求耗时: 1.441秒
2025-06-26 14:09:37 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-19b15aca3e3fab896a7fd0fd"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:37 | INFO     | utils.http_client:request:138 - 请求耗时: 1.531秒
2025-06-26 14:09:37 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-2828f4b20ceb2e0e3f4fd63f"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:37 | INFO     | utils.http_client:request:138 - 请求耗时: 1.576秒
2025-06-26 14:09:37 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-1281802b517bd1224d247f29"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:37 | INFO     | utils.http_client:request:138 - 请求耗时: 1.579秒
2025-06-26 14:09:37 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-5f05bf1929ad10c61887c081"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:37 | INFO     | utils.http_client:request:138 - 请求耗时: 1.580秒
2025-06-26 14:09:37 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-0943ad7e591830ff2c923c64"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:37 | INFO     | utils.http_client:request:138 - 请求耗时: 1.584秒
2025-06-26 14:09:37 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-64fc21e97e9b4f8c2273a3a3"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:37 | INFO     | utils.http_client:request:138 - 请求耗时: 1.589秒
2025-06-26 14:09:37 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:37 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-15319faf1a1758844914a862"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:37 | INFO     | utils.http_client:request:138 - 请求耗时: 1.819秒
2025-06-26 14:09:38 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:09:38 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:37 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:38 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "38",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce421-1fa6837b29f2cb7813d529ca"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=38&os=ios"
}
2025-06-26 14:09:38 | INFO     | utils.http_client:request:138 - 请求耗时: 1.907秒
2025-06-26 14:09:38 | INFO     | tests.api.test_check_update_api:test_concurrent_requests:163 - 并发测试结果: 成功10/10, 总耗时1.92秒
2025-06-26 14:09:38 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_concurrent_requests
2025-06-26 14:09:38 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_wrong_http_methods[POST]
2025-06-26 14:09:38 | INFO     | utils.http_client:_log_request:64 - 发送POST请求: https://httpbin.org/get
2025-06-26 14:09:38 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 64, 'os': 'ios'}
2025-06-26 14:09:38 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:38 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 405
2025-06-26 14:09:38 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:38 GMT', 'Content-Type': 'text/html', 'Content-Length': '178', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Allow': 'OPTIONS, GET, HEAD', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:38 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>405 Method Not Allowed</title>
<h1>Method Not Allowed</h1>
<p>The method is not allowed for the requested URL.</p>
...
2025-06-26 14:09:38 | INFO     | utils.http_client:request:138 - 请求耗时: 0.742秒
2025-06-26 14:09:38 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_wrong_http_methods[POST]
2025-06-26 14:09:38 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_wrong_http_methods[PUT]
2025-06-26 14:09:38 | INFO     | utils.http_client:_log_request:64 - 发送PUT请求: https://httpbin.org/get
2025-06-26 14:09:38 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 61, 'os': 'android'}
2025-06-26 14:09:38 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:39 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 405
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:39 GMT', 'Content-Type': 'text/html', 'Content-Length': '178', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Allow': 'OPTIONS, GET, HEAD', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>405 Method Not Allowed</title>
<h1>Method Not Allowed</h1>
<p>The method is not allowed for the requested URL.</p>
...
2025-06-26 14:09:39 | INFO     | utils.http_client:request:138 - 请求耗时: 0.620秒
2025-06-26 14:09:39 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_wrong_http_methods[PUT]
2025-06-26 14:09:39 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_wrong_http_methods[DELETE]
2025-06-26 14:09:39 | INFO     | utils.http_client:_log_request:64 - 发送DELETE请求: https://httpbin.org/get
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 12, 'os': 'ios'}
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:39 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 405
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:39 GMT', 'Content-Type': 'text/html', 'Content-Length': '178', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Allow': 'HEAD, GET, OPTIONS', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>405 Method Not Allowed</title>
<h1>Method Not Allowed</h1>
<p>The method is not allowed for the requested URL.</p>
...
2025-06-26 14:09:39 | INFO     | utils.http_client:request:138 - 请求耗时: 0.273秒
2025-06-26 14:09:39 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_wrong_http_methods[DELETE]
2025-06-26 14:09:39 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_wrong_http_methods[PATCH]
2025-06-26 14:09:39 | INFO     | utils.http_client:_log_request:64 - 发送PATCH请求: https://httpbin.org/get
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 61, 'os': 'ios'}
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:39 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 405
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:39 GMT', 'Content-Type': 'text/html', 'Content-Length': '178', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Allow': 'GET, HEAD, OPTIONS', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>405 Method Not Allowed</title>
<h1>Method Not Allowed</h1>
<p>The method is not allowed for the requested URL.</p>
...
2025-06-26 14:09:39 | INFO     | utils.http_client:request:138 - 请求耗时: 0.265秒
2025-06-26 14:09:39 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_wrong_http_methods[PATCH]
2025-06-26 14:09:39 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_version_update_scenarios[scenario0]
2025-06-26 14:09:39 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 100, 'os': 'android'}
2025-06-26 14:09:39 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:40 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:40 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:40 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:40 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:40 | INFO     | utils.http_client:request:138 - 请求耗时: 0.282秒
2025-06-26 14:09:40 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:40 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_version_update_scenarios[scenario0]
2025-06-26 14:09:40 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_version_update_scenarios[scenario1]
2025-06-26 14:09:40 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:40 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 98, 'os': 'android'}
2025-06-26 14:09:40 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:41 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:41 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:40 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:41 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:41 | INFO     | utils.http_client:request:138 - 请求耗时: 0.721秒
2025-06-26 14:09:41 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:41 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_version_update_scenarios[scenario1]
2025-06-26 14:09:41 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_version_update_scenarios[scenario2]
2025-06-26 14:09:41 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:41 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 90, 'os': 'android'}
2025-06-26 14:09:41 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:42 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:42 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:42 | INFO     | utils.http_client:request:138 - 请求耗时: 1.069秒
2025-06-26 14:09:42 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:42 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_version_update_scenarios[scenario2]
2025-06-26 14:09:42 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_version_update_scenarios[scenario3]
2025-06-26 14:09:42 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 95, 'os': 'ios'}
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:42 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:42 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:42 | INFO     | utils.http_client:request:138 - 请求耗时: 0.276秒
2025-06-26 14:09:42 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:42 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_version_update_scenarios[scenario3]
2025-06-26 14:09:42 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_force_update_logic
2025-06-26 14:09:42 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 95, 'os': 'android'}
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:42 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:42 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:42 | INFO     | utils.http_client:request:138 - 请求耗时: 0.435秒
2025-06-26 14:09:42 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:42 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_force_update_logic
2025-06-26 14:09:42 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_new_update_logic
2025-06-26 14:09:42 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 99, 'os': 'android'}
2025-06-26 14:09:42 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:43 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:43 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:43 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:43 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:43 | INFO     | utils.http_client:request:138 - 请求耗时: 0.310秒
2025-06-26 14:09:43 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:43 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_new_update_logic
2025-06-26 14:09:43 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_platform_consistency[android-android]
2025-06-26 14:09:43 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:43 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 97, 'os': 'android'}
2025-06-26 14:09:43 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:43 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:43 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:43 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:43 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:43 | INFO     | utils.http_client:request:138 - 请求耗时: 0.281秒
2025-06-26 14:09:43 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:43 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_platform_consistency[android-android]
2025-06-26 14:09:43 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_platform_consistency[ios-ios]
2025-06-26 14:09:43 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:43 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 97, 'os': 'ios'}
2025-06-26 14:09:43 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:44 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:44 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:44 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:44 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:44 | INFO     | utils.http_client:request:138 - 请求耗时: 0.881秒
2025-06-26 14:09:44 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:44 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_platform_consistency[ios-ios]
2025-06-26 14:09:44 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_version_rollback_scenario
2025-06-26 14:09:44 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:44 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 110, 'os': 'android'}
2025-06-26 14:09:44 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:45 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:45 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:45 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:45 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:45 | INFO     | utils.http_client:request:138 - 请求耗时: 1.037秒
2025-06-26 14:09:45 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=400, 实际=404
2025-06-26 14:09:45 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_version_rollback_scenario
2025-06-26 14:09:45 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_business_rule_boundaries
2025-06-26 14:09:45 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/api/v1/app/version/checkUpdate
2025-06-26 14:09:45 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 96, 'os': 'android'}
2025-06-26 14:09:45 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:09:45 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 404
2025-06-26 14:09:45 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:09:45 GMT', 'Content-Type': 'text/html', 'Content-Length': '233', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:09:45 | DEBUG    | utils.http_client:_log_response:83 - 响应体: <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
...
2025-06-26 14:09:45 | INFO     | utils.http_client:request:138 - 请求耗时: 0.271秒
2025-06-26 14:09:45 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=404
2025-06-26 14:09:45 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_business_rule_boundaries
2025-06-26 14:09:45 | INFO     | utils.http_client:close:182 - HTTP客户端会话已关闭
2025-06-26 14:10:59 | INFO     | conftest:config_data:61 - 加载配置完成，当前环境: test
2025-06-26 14:10:59 | INFO     | utils.http_client:__init__:48 - HTTP客户端初始化完成: https://httpbin.org
2025-06-26 14:10:59 | DEBUG    | utils.http_client:set_default_headers:54 - 设置默认请求头: {'app-token': 'your-app-token-here', 'platform': 'android', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:10:59 | INFO     | conftest:test_setup_teardown:100 - 开始执行测试: test_valid_request
2025-06-26 14:10:59 | INFO     | utils.http_client:_log_request:64 - 发送GET请求: https://httpbin.org/get
2025-06-26 14:10:59 | DEBUG    | utils.http_client:_log_request:66 - 请求参数: {'current_version': 57, 'os': 'ios'}
2025-06-26 14:10:59 | DEBUG    | utils.http_client:_log_request:73 - 请求头: {'app-token': '***', 'platform': 'ios', 'version': '1.0.0', 'Content-Type': 'application/json'}
2025-06-26 14:11:00 | INFO     | utils.http_client:_log_response:77 - 响应状态码: 200
2025-06-26 14:11:00 | DEBUG    | utils.http_client:_log_response:78 - 响应头: {'Date': 'Thu, 26 Jun 2025 06:11:00 GMT', 'Content-Type': 'application/json', 'Content-Length': '516', 'Connection': 'keep-alive', 'Server': 'gunicorn/19.9.0', 'Access-Control-Allow-Origin': '*', 'Access-Control-Allow-Credentials': 'true'}
2025-06-26 14:11:00 | DEBUG    | utils.http_client:_log_response:81 - 响应体: {
  "args": {
    "current_version": "57",
    "os": "ios"
  },
  "headers": {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    "App-Token": "valid-token-12345",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
    "Platform": "ios",
    "User-Agent": "python-requests/2.31.0",
    "Version": "1.0.0",
    "X-Amzn-Trace-Id": "Root=1-685ce474-79306a616328c17b30d0c5f0"
  },
  "origin": "**************",
  "url": "https://httpbin.org/get?current_version=57&os=ios"
}
2025-06-26 14:11:00 | INFO     | utils.http_client:request:138 - 请求耗时: 1.352秒
2025-06-26 14:11:00 | INFO     | utils.response_validator:validate_status_code:48 - 验证状态码: 期望=200, 实际=200
2025-06-26 14:11:00 | INFO     | utils.response_validator:validate_json_format:64 - 响应JSON格式验证通过
2025-06-26 14:11:00 | INFO     | utils.response_validator:validate_check_update_response:164 - httpbin.org响应格式验证通过
2025-06-26 14:11:00 | INFO     | utils.response_validator:validate_response_time:134 - 响应时间验证: 1.348秒 (最大允许: 5.0秒)
2025-06-26 14:11:00 | INFO     | utils.response_validator:validate_check_update_response:169 - 演示版本响应验证通过
2025-06-26 14:11:00 | INFO     | conftest:test_setup_teardown:104 - 测试执行完成: test_valid_request
2025-06-26 14:11:00 | INFO     | utils.http_client:close:182 - HTTP客户端会话已关闭
