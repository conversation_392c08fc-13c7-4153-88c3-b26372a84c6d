"""
pytest配置文件
提供全局fixtures和配置
"""
import os
import pytest
import yaml
import allure
from pathlib import Path
from typing import Dict, Any
from utils.http_client import HttpClient
from utils.logger import setup_logger

# 设置日志
logger = setup_logger()

def pytest_configure(config):
    """pytest配置钩子"""
    # 创建报告目录
    reports_dir = Path("reports")
    reports_dir.mkdir(exist_ok=True)
    
    # 设置allure环境信息
    allure_results_dir = reports_dir / "allure-results"
    allure_results_dir.mkdir(exist_ok=True)
    
    # 写入环境信息
    env_properties = allure_results_dir / "environment.properties"
    with open(env_properties, "w", encoding="utf-8") as f:
        f.write(f"Environment={os.getenv('TEST_ENV', 'test')}\n")
        f.write("Test.Type=API自动化测试\n")
        f.write("Framework=pytest+requests+allure\n")
        f.write("Platform=充电APP后端接口\n")

def pytest_collection_modifyitems(config, items):
    """修改测试用例收集"""
    for item in items:
        # 为中文测试用例名称添加编码
        if "checkupdate" in item.nodeid.lower():
            item.add_marker(pytest.mark.checkupdate)

@pytest.fixture(scope="session")
def config_data() -> Dict[str, Any]:
    """加载配置文件"""
    config_path = Path("config/config.yaml")
    if not config_path.exists():
        pytest.fail(f"配置文件不存在: {config_path}")
    
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    
    # 获取环境变量指定的环境，默认为test
    env = os.getenv("TEST_ENV", config.get("default_env", "test"))
    if env not in config["environments"]:
        pytest.fail(f"不支持的环境: {env}")
    
    # 合并环境配置到根级别
    config.update(config["environments"][env])
    config["current_env"] = env
    
    logger.info(f"加载配置完成，当前环境: {env}")
    return config

@pytest.fixture(scope="session")
def http_client(config_data) -> HttpClient:
    """HTTP客户端fixture"""
    client = HttpClient(
        base_url=config_data["base_url"],
        timeout=config_data["timeout"],
        retry_times=config_data["retry_times"],
        verify_ssl=config_data["verify_ssl"]
    )
    
    # 设置默认headers
    default_headers = {
        "app-token": config_data["auth"]["app_token"],
        "platform": config_data["auth"]["platform"],
        "version": config_data["auth"]["version"],
        "Content-Type": "application/json"
    }
    client.set_default_headers(default_headers)
    
    yield client
    client.close()

@pytest.fixture
def test_data(config_data) -> Dict[str, Any]:
    """测试数据fixture"""
    return config_data["test_data"]

@pytest.fixture
def business_rules(config_data) -> Dict[str, Any]:
    """业务规则fixture"""
    return config_data["business_rules"]

@pytest.fixture(autouse=True)
def test_setup_teardown(request):
    """测试用例前后置处理"""
    test_name = request.node.name
    logger.info(f"开始执行测试: {test_name}")
    
    yield
    
    logger.info(f"测试执行完成: {test_name}")

# 命令行选项
def pytest_addoption(parser):
    """添加命令行选项"""
    parser.addoption(
        "--env",
        action="store",
        default="test",
        help="指定测试环境: dev, test, prod"
    )
    parser.addoption(
        "--smoke",
        action="store_true",
        help="只运行冒烟测试"
    )

@pytest.fixture(scope="session")
def cmdopt(request):
    """命令行选项fixture"""
    return {
        "env": request.config.getoption("--env"),
        "smoke": request.config.getoption("--smoke")
    }
