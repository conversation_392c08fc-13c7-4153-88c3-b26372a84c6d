"""
Allure报告配置
"""
import os
import json
from pathlib import Path

def setup_allure_environment():
    """设置Allure环境信息"""
    reports_dir = Path("reports/allure-results")
    reports_dir.mkdir(parents=True, exist_ok=True)
    
    # 环境信息
    environment_data = {
        "Environment": os.getenv("TEST_ENV", "test"),
        "Test.Type": "API自动化测试",
        "Framework": "pytest + requests + allure",
        "Platform": "充电APP后端接口",
        "Python.Version": "3.8+",
        "Base.URL": os.getenv("BASE_URL", "https://test-api.charging-app.com")
    }
    
    # 写入environment.properties
    env_file = reports_dir / "environment.properties"
    with open(env_file, "w", encoding="utf-8") as f:
        for key, value in environment_data.items():
            f.write(f"{key}={value}\n")
    
    # 写入categories.json（测试分类）
    categories = [
        {
            "name": "接口错误",
            "matchedStatuses": ["failed"],
            "messageRegex": ".*status.*code.*"
        },
        {
            "name": "业务逻辑错误", 
            "matchedStatuses": ["failed"],
            "messageRegex": ".*业务.*逻辑.*|.*版本.*更新.*"
        },
        {
            "name": "超时错误",
            "matchedStatuses": ["failed"],
            "messageRegex": ".*timeout.*|.*响应时间.*"
        },
        {
            "name": "参数验证错误",
            "matchedStatuses": ["failed"],
            "messageRegex": ".*参数.*|.*validation.*"
        },
        {
            "name": "网络错误",
            "matchedStatuses": ["broken"],
            "messageRegex": ".*connection.*|.*network.*"
        }
    ]
    
    categories_file = reports_dir / "categories.json"
    with open(categories_file, "w", encoding="utf-8") as f:
        json.dump(categories, f, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    setup_allure_environment()
    print("Allure环境配置完成")
