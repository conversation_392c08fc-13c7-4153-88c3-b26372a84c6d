#!/usr/bin/env python3
"""
测试执行脚本
提供不同的测试执行选项
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path
from allure_config import setup_allure_environment

def run_command(cmd, description=""):
    """执行命令并处理结果"""
    print(f"\n{'='*50}")
    print(f"执行: {description or cmd}")
    print(f"{'='*50}")
    
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.stdout:
        print("STDOUT:")
        print(result.stdout)
    
    if result.stderr:
        print("STDERR:")
        print(result.stderr)
    
    if result.returncode != 0:
        print(f"命令执行失败，退出码: {result.returncode}")
        return False
    
    print("命令执行成功")
    return True

def install_dependencies():
    """安装依赖"""
    print("安装测试依赖...")
    return run_command("pip install -r requirements.txt", "安装依赖")

def setup_environment():
    """设置测试环境"""
    print("设置测试环境...")
    
    # 创建必要的目录
    dirs = ["logs", "reports", "reports/allure-results"]
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    # 设置Allure环境
    setup_allure_environment()
    
    print("环境设置完成")
    return True

def run_smoke_tests(env="test"):
    """运行冒烟测试"""
    cmd = f"TEST_ENV={env} pytest -m smoke --alluredir=reports/allure-results -v"
    return run_command(cmd, "运行冒烟测试")

def run_api_tests(env="test"):
    """运行API测试"""
    cmd = f"TEST_ENV={env} pytest -m api --alluredir=reports/allure-results -v"
    return run_command(cmd, "运行API测试")

def run_business_tests(env="test"):
    """运行业务逻辑测试"""
    cmd = f"TEST_ENV={env} pytest -m business --alluredir=reports/allure-results -v"
    return run_command(cmd, "运行业务逻辑测试")

def run_checkupdate_tests(env="test"):
    """运行checkUpdate相关测试"""
    cmd = f"TEST_ENV={env} pytest -m checkupdate --alluredir=reports/allure-results -v"
    return run_command(cmd, "运行checkUpdate测试")

def run_all_tests(env="test", parallel=False):
    """运行所有测试"""
    parallel_option = "-n auto" if parallel else ""
    cmd = f"TEST_ENV={env} pytest {parallel_option} --alluredir=reports/allure-results -v"
    return run_command(cmd, "运行所有测试")

def generate_allure_report():
    """生成Allure报告"""
    # 生成报告
    if not run_command("allure generate reports/allure-results -o reports/allure-report --clean", 
                      "生成Allure报告"):
        return False
    
    print("\n报告生成完成！")
    print("HTML报告位置: reports/report.html")
    print("Allure报告位置: reports/allure-report/index.html")
    print("\n查看Allure报告:")
    print("allure serve reports/allure-results")
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="充电APP自动化测试执行脚本")
    parser.add_argument("--env", choices=["dev", "test", "prod"], default="test",
                       help="测试环境 (默认: test)")
    parser.add_argument("--install", action="store_true", help="安装依赖")
    parser.add_argument("--setup", action="store_true", help="设置环境")
    parser.add_argument("--smoke", action="store_true", help="运行冒烟测试")
    parser.add_argument("--api", action="store_true", help="运行API测试")
    parser.add_argument("--business", action="store_true", help="运行业务逻辑测试")
    parser.add_argument("--checkupdate", action="store_true", help="运行checkUpdate测试")
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    parser.add_argument("--parallel", action="store_true", help="并行执行测试")
    parser.add_argument("--report", action="store_true", help="生成Allure报告")
    parser.add_argument("--serve", action="store_true", help="启动Allure报告服务")
    
    args = parser.parse_args()
    
    # 如果没有指定任何操作，显示帮助
    if not any([args.install, args.setup, args.smoke, args.api, args.business, 
               args.checkupdate, args.all, args.report, args.serve]):
        parser.print_help()
        return
    
    success = True
    
    # 安装依赖
    if args.install:
        success = install_dependencies() and success
    
    # 设置环境
    if args.setup:
        success = setup_environment() and success
    
    # 运行测试
    if args.smoke:
        success = run_smoke_tests(args.env) and success
    elif args.api:
        success = run_api_tests(args.env) and success
    elif args.business:
        success = run_business_tests(args.env) and success
    elif args.checkupdate:
        success = run_checkupdate_tests(args.env) and success
    elif args.all:
        success = run_all_tests(args.env, args.parallel) and success
    
    # 生成报告
    if args.report:
        success = generate_allure_report() and success
    
    # 启动报告服务
    if args.serve:
        print("启动Allure报告服务...")
        os.system("allure serve reports/allure-results")
    
    if not success:
        print("\n❌ 执行过程中出现错误")
        sys.exit(1)
    else:
        print("\n✅ 执行完成")

if __name__ == "__main__":
    main()
