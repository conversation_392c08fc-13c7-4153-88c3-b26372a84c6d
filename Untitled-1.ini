

## 获取最新版本


**接口地址**:`/api/v1/app/version/getNewVersion`


**请求方式**:`GET`


**请求数据类型**:`*`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | in    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|app-token|令牌|header|true|string||
|os|os|query|true|string||
|platform|平台|header|true|string||
|version|版本号|header|true|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|SimpleResponse«VersionVo»|
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|data||VersionVo|VersionVo|
|&emsp;&emsp;create_date_time||string(date-time)||
|&emsp;&emsp;description||string||
|&emsp;&emsp;download_url||string||
|&emsp;&emsp;force_update||boolean||
|&emsp;&emsp;min_version||integer(int32)||
|&emsp;&emsp;new_update||boolean||
|&emsp;&emsp;new_version||integer(int32)||
|&emsp;&emsp;os||string||
|&emsp;&emsp;version_name||string||
|message|返回消息,成功为success|string||
|status|返回状态,成功为200|integer(int32)|integer(int32)|


**响应示例**:
```javascript
{
	"data": {
		"create_date_time": "",
		"description": "",
		"download_url": "",
		"force_update": true,
		"min_version": 0,
		"new_update": true,
		"new_version": 0,
		"os": "",
		"version_name": ""
	},
	"message": "success",
	"status": 200
}
```