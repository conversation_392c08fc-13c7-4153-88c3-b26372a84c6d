# 环境配置
environments:
  dev:
    base_url: "https://dev-api.charging-app.com"
    timeout: 30
    retry_times: 3
    verify_ssl: false
  
  test:
    base_url: "https://test-api.charging-app.com"
    timeout: 30
    retry_times: 3
    verify_ssl: false
  
  prod:
    base_url: "https://api.charging-app.com"
    timeout: 30
    retry_times: 2
    verify_ssl: true

# 默认环境
default_env: "test"

# 认证配置
auth:
  app_token: "your-app-token-here"
  platform: "android"
  version: "1.0.0"

# 测试数据配置
test_data:
  valid_versions: [1, 2, 3, 10, 100]
  invalid_versions: [-1, 0, "abc", null]
  platforms: ["android", "ios"]
  invalid_platforms: ["", "windows", "web", null]

# 业务规则配置
business_rules:
  # 版本更新规则
  version_update:
    force_update_threshold: 5  # 版本差距超过5个版本强制更新
    new_update_threshold: 1    # 版本差距超过1个版本提示更新
  
  # 平台支持
  supported_platforms: ["android", "ios"]
  
  # 版本号范围
  min_version: 1
  max_version: 999

# 报告配置
reports:
  allure:
    environment_properties:
      - "Environment"
      - "Test.Type"
      - "Browser"
      - "Platform"
  
  html:
    title: "充电APP自动化测试报告"
    description: "基于pytest + requests + allure的接口自动化测试"
