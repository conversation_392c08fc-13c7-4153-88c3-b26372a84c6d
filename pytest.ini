[tool:pytest]
# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 标记
markers =
    smoke: 冒烟测试
    regression: 回归测试
    api: API接口测试
    business: 业务逻辑测试
    slow: 慢速测试
    critical: 关键功能测试
    checkupdate: checkUpdate接口相关测试

# 输出选项
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --alluredir=reports/allure-results
    --html=reports/report.html
    --self-contained-html

# 最小版本要求
minversion = 6.0

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 过滤警告
filterwarnings =
    ignore::urllib3.exceptions.InsecureRequestWarning
    ignore::DeprecationWarning
